import pickle

def getPickleData(name='indexer', mode='pkl'):
    with open(f'./data/TencentGR_1k/{name}.{mode}', 'rb') as f:
        if(mode == 'pkl'):
            indexData = pickle.load(f)
        else:
            indexData = f.readlines()

    print(type(indexData))
    return indexData

def getIndexerData(indexData):
    print(indexData.keys())

    for (inK, inV) in indexData.items():
        print(f"==={inK}, type: {type(inV)}===")
        count=0
        kkLen=0
        kLen=0

        if(inK == 'f'):
            user_sparse = ['103', '104', '105', '109']
            user_array = ['106', '107', '108', '110']
            item_sparse = [
                '100',
                '117',
                '111',
                '118',
                '101',
                '102',
                '119',
                '120',
                '114',
                '112',
                '121',
                '115',
                '122',
                '116',
                ]
            user_sparse_dims = []
            user_array_dims = []
            item_sparse_dims = []
            print(f"inV keyList: {inV.keys()}")
            for (kk, vv) in inV.items():

                kkLen+=1
                kLen=0
                count = 0
                for (k, v) in vv.items():
                    

                    kLen+=1
                    if(count > 5):
                        continue
                    print(f"inV key: {kk}, kk key: {k} kk value: {v}")
                    count+=1
                print(f"kLen: {kLen}")

                if(kk in user_array):
                    user_array_dims.append(kLen)
                elif(kk in item_sparse):
                    item_sparse_dims.append(kLen)
                else:
                    user_sparse_dims.append(kLen)
            print(f"kkLen: {kkLen}")

            print(f"user_array: {user_array}")
            print(f"user_array_dims: {user_array_dims}")
            print(f"item_sparse: {item_sparse}")
            print(f"item_sparse_dims: {item_sparse_dims}")
            print(f"user_sparse: {user_sparse}")
            print(f"user_sparse_dims: {user_sparse_dims}")

            continue

        kkLen=0
        kLen=0
        count=0
        for (k, v) in inV.items():
            kLen+=1
            if(count > 5):
                continue
            print(f"inV key: {k}, inV value: {v}")
            count+=1

        print(f"kLen: {kLen}")

def getSeqOffsetsData(data):
    lLen = len(data)
    print(f"len: {lLen}")
    count = 0
    for l in data:
        if(count > 5):
            break
        count+=1
        print(f"type: {type(l)}, value: {l}")
        # print(type(l))

import json
import os
import json
import pickle
import struct
from pathlib import Path

mm_path = "./data/TencentGR_1k/creative_emb"
feat_id = 81
shape = 32
base_path = Path(mm_path, f'emb_{feat_id}_{shape}')
for json_name in os.listdir(base_path):
    if('part' not in json_name):
        continue
    json_file = os.path.join(base_path, json_name)
    with open(json_file, 'r', encoding='utf-8') as file:
        for line in file:
            data_dict_origin = json.loads(line.strip())
            break
    break
print(data_dict_origin.keys())
for (k,v) in data_dict_origin.items():
    print(f"k key: {k}")
data = data_dict_origin['emb']
print(f"emb v len: {len(data)}")

# data = getPickleData(name='indexer')
# print(f"len: {len(data)}")
# getSeqOffsetsData(data)
# getIndexerData(data)
