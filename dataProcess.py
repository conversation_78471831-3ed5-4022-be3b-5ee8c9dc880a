import pickle

def getPickleData(name='indexer', mode='pkl'):
    with open(f'./data/TencentGR_1k/{name}.{mode}', 'rb') as f:
        if(mode == 'pkl'):
            indexData = pickle.load(f)
        else:
            indexData = f.readlines()

    print(type(indexData))
    return indexData

def getIndexerData(indexData):
    print(indexData.keys())

    for (inK, inV) in indexData.items():
        print(f"==={inK}, type: {type(inV)}===")
        count=0
        kkLen=0
        kLen=0

        if(inK == 'f'):
            user_sparse = ['103', '104', '105', '109']
            user_array = ['106', '107', '108', '110']
            item_sparse = [
                '100',
                '117',
                '111',
                '118',
                '101',
                '102',
                '119',
                '120',
                '114',
                '112',
                '121',
                '115',
                '122',
                '116',
                ]
            user_sparse_dims = []
            user_array_dims = []
            item_sparse_dims = []
            print(f"inV keyList: {inV.keys()}")
            for (kk, vv) in inV.items():

                kkLen+=1
                kLen=0
                count = 0
                for (k, v) in vv.items():
                    

                    kLen+=1
                    if(count > 5):
                        continue
                    print(f"inV key: {kk}, kk key: {k} kk value: {v}")
                    count+=1
                print(f"kLen: {kLen}")

                if(kk in user_array):
                    user_array_dims.append(kLen)
                elif(kk in item_sparse):
                    item_sparse_dims.append(kLen)
                else:
                    user_sparse_dims.append(kLen)
            print(f"kkLen: {kkLen}")

            print(f"user_array: {user_array}")
            print(f"user_array_dims: {user_array_dims}")
            print(f"item_sparse: {item_sparse}")
            print(f"item_sparse_dims: {item_sparse_dims}")
            print(f"user_sparse: {user_sparse}")
            print(f"user_sparse_dims: {user_sparse_dims}")

            continue

        kkLen=0
        kLen=0
        count=0
        for (k, v) in inV.items():
            kLen+=1
            if(count > 5):
                continue
            print(f"inV key: {k}, inV value: {v}")
            count+=1

        print(f"kLen: {kLen}")

def getSeqOffsetsData(data):
    lLen = len(data)
    print(f"len: {lLen}")
    count = 0
    for l in data:
        if(count > 5):
            break
        count+=1
        print(f"type: {type(l)}, value: {l}")
        # print(type(l))

import os
import json
import pickle
import struct
from pathlib import Path

def getEmbData(feat_id, shape):
    mm_path = "./data/TencentGR_1k/creative_emb"
    # feat_id = 81
    # shape = 32
    base_path = Path(mm_path, f'emb_{feat_id}_{shape}')
    for json_name in os.listdir(base_path):
        if('part' not in json_name):
            continue
        json_file = os.path.join(base_path, json_name)
        with open(json_file, 'r', encoding='utf-8') as file:
            for line in file:
                data_dict_origin = json.loads(line.strip())
                break
        break
    print(data_dict_origin.keys())
    for (k,v) in data_dict_origin.items():
        print(f"k key: {k}")
    data = data_dict_origin['emb']
    print(f"emb v len: {len(data)}")


def getSeqData(data):
    d = data[0]
    print(d)
    null = "null"
    d = [[640, 47086, null, {"112": 14, "117": 80, "118": 249, "119": 1174, "120": 118, "100": 6, "101": 18, "102": 6779, "122": 8307, "114": 16, "116": 6, "121": 28737, "111": 42338}, 0, 1745813860], [640, 53920, null, {"112": 14, "117": 80, "118": 249, "119": 305, "120": 11, "100": 6, "101": 28, "102": 10123, "122": 5741, "114": 4, "116": 15, "121": 1485, "111": 30361}, 0, 1745825229], [640, 25129, null, {"112": 14, "117": 265, "118": 689, "119": 1431, "120": 695, "100": 6, "101": 28, "102": 5857, "122": 4940, "114": 4, "116": 15, "121": 26028, "111": 56968}, 0, 1745825832], [640, 42664, null, {"100": 6, "101": 18, "102": 6800, "122": 6391, "114": 16, "115": 17, "116": 13, "121": 34027, "111": 2279}, 0, 1745911925], [640, 20488, null, {"112": 14, "117": 80, "118": 160, "119": 327, "120": 931, "100": 6, "101": 18, "102": 2652, "122": 542, "114": 9, "116": 6, "121": 12545, "111": 28915}, 0, 1745913815], [640, 7933, null, {"112": 14, "117": 219, "118": 688, "119": 1007, "120": 118, "100": 6, "101": 18, "102": 3824, "122": 3100, "114": 16, "115": 51, "116": 13, "121": 22834, "111": 3386}, 0, 1745919732], [640, 50563, null, {"112": 14, "117": 50, "118": 571, "119": 758, "120": 659, "100": 6, "101": 28, "102": 1730, "122": 1711, "114": 16, "116": 15, "121": 47392, "111": 40359}, 0, 1745922353], [640, 5470, null, {"112": 14, "117": 50, "118": 667, "119": 4, "120": 118, "100": 6, "101": 28, "102": 9290, "122": 4123, "114": 16, "116": 15, "121": 32626, "111": 36145}, 0, 1745926449], [640, 23981, null, {"112": 14, "117": 104, "118": 309, "119": 1358, "120": 118, "100": 6, "101": 28, "102": 4936, "122": 5973, "114": 16, "116": 15, "121": 3525, "111": 15503}, 0, 1745929671], [640, 27358, null, {"112": 14, "117": 219, "118": 391, "119": 1467, "120": 118, "100": 6, "101": 18, "102": 3824, "122": 3100, "114": 16, "115": 51, "116": 13, "121": 22834, "111": 7891}, 0, 1746030378], [640, 8731, null, {"112": 14, "117": 33, "118": 538, "119": 668, "120": 1202, "100": 6, "101": 29, "102": 9789, "122": 8695, "114": 16, "115": 44, "116": 13, "121": 21545, "111": 2120}, 0, 1746032496], [640, 51412, null, {"112": 14, "117": 254, "118": 358, "119": 370, "120": 1091, "100": 6, "101": 18, "102": 9376, "122": 1905, "114": 16, "116": 6, "121": 1033, "111": 31186}, 0, 1746088842], [640, 52446, null, {"112": 14, "117": 254, "118": 130, "119": 1044, "120": 288, "100": 6, "101": 28, "102": 1570, "122": 6042, "114": 4, "116": 15, "121": 6478, "111": 54250}, 0, 1746090375], [640, 37488, null, {"112": 14, "117": 80, "118": 104, "119": 296, "120": 118, "100": 6, "101": 28, "102": 10950, "122": 9214, "114": 16, "116": 15, "121": 34837, "111": 28299}, 0, 1746248172], [640, 17184, null, {"112": 14, "117": 254, "118": 295, "119": 766, "120": 385, "100": 6, "101": 28, "102": 89, "122": 20, "114": 16, "116": 15, "121": 30971, "111": 22770}, 0, 1746256800], [640, 25287, null, {"112": 14, "117": 238, "118": 290, "119": 75, "120": 1026, "100": 6, "101": 5, "102": 4251, "122": 8464, "114": 16, "115": 88, "116": 13, "121": 37754, "111": 9146}, 1, 1746259184], [640, 13558, null, {"112": 14, "117": 121, "118": 569, "119": 310, "120": 70, "100": 6, "101": 28, "102": 10640, "122": 6779, "114": 9, "116": 15, "121": 28673, "111": 38696}, 1, 1746265540], [640, 53227, null, {"112": 14, "117": 196, "118": 71, "119": 1587, "120": 118, "100": 6, "101": 28, "102": 7874, "122": 1787, "114": 4, "116": 15, "121": 3526, "111": 3096}, 0, 1746265944], [640, 51004, null, {"112": 14, "117": 81, "118": 431, "119": 1635, "120": 779, "100": 6, "101": 28, "102": 2475, "122": 6643, "114": 16, "116": 15, "121": 21546, "111": 52982}, 0, 1746417402], [640, 23982, null, {"112": 14, "117": 196, "118": 434, "119": 1278, "120": 11, "100": 6, "101": 28, "102": 150, "122": 1224, "114": 16, "116": 15, "121": 15888, "111": 23785}, 1, 1746417445], [640, 8117, null, {"112": 14, "117": 219, "118": 391, "119": 1467, "120": 118, "100": 6, "101": 18, "102": 3824, "122": 3100, "114": 16, "115": 51, "116": 13, "121": 4006, "111": 51969}, 0, 1746420797], [640, 52103, null, {"112": 14, "117": 265, "118": 689, "119": 1431, "120": 426, "100": 6, "101": 28, "102": 137, "122": 1483, "114": 4, "116": 15, "121": 13353, "111": 4816}, 0, 1746518888], [640, 14669, null, {"112": 14, "117": 80, "118": 107, "119": 393, "120": 555, "100": 6, "101": 28, "102": 2221, "122": 4507, "114": 4, "116": 15, "121": 19245, "111": 49917}, 0, 1746521523], [640, 9811, null, {"112": 14, "117": 80, "118": 402, "119": 1699, "120": 1305, "100": 6, "101": 28, "102": 10043, "122": 5433, "114": 3, "116": 15, "121": 47052, "111": 1754}, 0, 1746523751], [640, 20863, null, {"112": 14, "117": 80, "118": 402, "119": 907, "120": 839, "100": 6, "101": 28, "102": 538, "122": 1469, "114": 16, "116": 15, "121": 18714, "111": 36665}, 0, 1746527640], [640, 31152, null, {"112": 14, "117": 223, "118": 78, "119": 1206, "120": 1, "100": 6, "101": 28, "102": 5258, "122": 2356, "114": 16, "116": 15, "121": 25915, "111": 34495}, 0, 1746531140], [640, 18579, null, {"112": 14, "117": 80, "118": 402, "119": 907, "120": 839, "100": 6, "101": 28, "102": 8592, "122": 10076, "114": 3, "116": 15, "121": 19246, "111": 28300}, 0, 1746531429], [640, 20489, null, {"112": 14, "117": 223, "118": 78, "119": 30, "120": 951, "100": 6, "101": 28, "102": 6631, "122": 9911, "114": 4, "116": 15, "121": 20728, "111": 41222}, 0, 1746587292], [640, 28479, null, {"112": 14, "117": 196, "118": 434, "119": 1278, "120": 11, "100": 6, "101": 28, "102": 150, "122": 1224, "114": 16, "116": 15, "121": 8930, "111": 5626}, 0, 1746587594], [640, 33822, null, {"112": 14, "117": 238, "118": 684, "119": 1122, "120": 118, "100": 6, "101": 5, "102": 9595, "122": 3551, "114": 16, "115": 88, "116": 13, "121": 12546, "111": 44572}, 0, 1746588790], [640, 34938, null, {"112": 14, "117": 254, "118": 358, "119": 370, "120": 781, "100": 6, "101": 18, "102": 8593, "122": 7724, "114": 16, "116": 6, "121": 11731, "111": 44739}, 0, 1746616194], [640, 49722, null, {"112": 14, "117": 219, "118": 147, "119": 324, "120": 118, "100": 6, "101": 29, "102": 6083, "122": 10915, "114": 16, "115": 17, "116": 13, "121": 14359, "111": 6781}, 0, 1746683011], [640, 28823, null, {"112": 14, "117": 238, "118": 290, "119": 75, "120": 1026, "100": 6, "101": 18, "102": 6447, "122": 2569, "114": 16, "115": 88, "116": 13, "121": 1034, "111": 42864}, 0, 1746683024], [640, 48736, null, {"112": 14, "117": 196, "118": 434, "119": 1007, "120": 118, "100": 6, "101": 28, "102": 150, "122": 1224, "114": 16, "116": 15, "121": 24246, "111": 10196}, 0, 1746700964], [640, 14248, null, {"112": 14, "117": 39, "118": 287, "119": 1100, "120": 118, "100": 6, "101": 5, "102": 3959, "122": 10702, "114": 16, "116": 6, "121": 29555, "111": 44573}, 0, 1746769355], [640, 2286, null, {"112": 14, "117": 5, "118": 92, "119": 384, "120": 1044, "100": 6, "101": 18, "102": 9829, "122": 5306, "114": 16, "115": 51, "116": 13, "121": 16962, "111": 24753}, 0, 1746770256], [640, 43530, null, {"112": 14, "117": 196, "118": 434, "119": 1278, "120": 118, "100": 6, "101": 28, "102": 150, "122": 1224, "114": 16, "116": 15, "121": 15888, "111": 14691}, 0, 1746777173], [640, 11595, null, {"112": 14, "117": 80, "118": 402, "119": 1699, "120": 1305, "100": 6, "101": 28, "102": 9055, "122": 3445, "114": 4, "116": 15, "121": 43680, "111": 6524}, 0, 1746777706], [640, 33450, null, {"112": 14, "117": 254, "118": 295, "119": 766, "120": 385, "100": 6, "101": 28, "102": 8497, "122": 5685, "114": 4, "116": 15, "121": 31853, "111": 54051}, 1, 1746781866], [640, 48013, null, {"112": 14, "117": 196, "118": 434, "119": 1278, "120": 118, "100": 6, "101": 28, "102": 150, "122": 1224, "114": 16, "116": 15, "121": 24871, "111": 38890}, 0, 1746782335], [640, 17378, null, {"112": 14, "117": 238, "118": 290, "119": 1547, "120": 49, "121": 25494, "100": 6, "101": 5, "102": 8594, "122": 2918, "111": 8492, "114": 16, "115": 88, "116": 13}, 0, 1746852698], [640, 36651, null, {"112": 14, "117": 238, "118": 290, "119": 1547, "120": 49, "121": 25916, "100": 6, "101": 5, "102": 8594, "122": 2918, "111": 49918, "114": 16, "115": 88, "116": 13}, 1, 1746855442], [640, 13902, null, {"112": 14, "117": 238, "118": 290, "119": 1547, "120": 49, "121": 44973, "100": 6, "101": 18, "102": 6283, "122": 2070, "111": 24141, "114": 16, "115": 88, "116": 13}, 0, 1746855839], [640, 48219, null, {"112": 14, "117": 5, "118": 92, "119": 384, "120": 1044, "121": 25064, "100": 6, "101": 18, "102": 9829, "122": 5306, "111": 31665, "114": 16, "115": 51, "116": 13}, 0, 1746856001], [640, 34621, null, {"112": 14, "117": 80, "118": 402, "119": 1699, "120": 464, "121": 12994, "100": 6, "101": 18, "102": 8802, "122": 5322, "111": 16024, "114": 16, "116": 6}, 0, 1746947999], [640, 6491, null, {"112": 14, "117": 223, "118": 15, "119": 999, "120": 1117, "121": 42983, "100": 6, "101": 28, "102": 9339, "122": 2759, "111": 22438, "114": 4, "116": 15}, 0, 1746973528], [640, 26479, null, {"112": 14, "117": 121, "118": 569, "119": 275, "120": 118, "121": 20729, "100": 6, "101": 18, "102": 10436, "122": 3012, "111": 45549, "114": 16, "115": 88, "116": 13}, 0, 1747027347], [640, 4295, null, {"112": 14, "117": 80, "118": 249, "119": 418, "120": 118, "121": 258, "100": 6, "101": 28, "102": 10567, "122": 494, "111": 33864, "114": 16, "116": 15}, 0, 1747058246], [640, 32508, null, {"112": 14, "117": 265, "118": 47, "119": 1597, "120": 203, "121": 45335, "100": 6, "101": 5, "102": 6386, "122": 798, "111": 17862, "114": 16, "116": 6}, 0, 1747058912], [640, 8118, null, {"112": 14, "117": 192, "118": 688, "119": 1007, "120": 118, "121": 39537, "100": 6, "101": 28, "102": 26, "122": 8533, "111": 22077, "114": 4, "116": 15}, 0, 1747123347], [640, 36478, null, {"112": 14, "117": 254, "118": 295, "119": 1568, "120": 401, "121": 2487, "100": 6, "101": 5, "102": 8658, "122": 90, "111": 42991, "114": 16, "116": 6}, 0, 1747123961], [640, 10033, null, {"112": 14, "117": 83, "118": 618, "119": 1245, "120": 1178, "121": 8468, "100": 6, "101": 18, "102": 3289, "122": 540, "111": 17095, "114": 16, "116": 6}, 0, 1747124208], [640, 35628, null, {"112": 14, "117": 5, "118": 92, "119": 1055, "120": 465, "121": 9022, "100": 6, "101": 18, "102": 5236, "122": 7386, "111": 39028, "114": 16, "115": 51, "116": 13}, 0, 1747126513], [640, 5930, null, {"112": 14, "117": 219, "118": 147, "119": 324, "120": 118, "121": 28495, "100": 6, "101": 29, "102": 6133, "122": 9539, "111": 30315, "114": 16, "115": 17, "116": 13}, 0, 1747219283], [640, 10568, null, {"112": 14, "117": 238, "118": 290, "119": 1547, "120": 49, "121": 46323, "100": 6, "101": 18, "102": 9099, "122": 8286, "111": 40360, "114": 16, "115": 88, "116": 13}, 0, 1747222825], [640, 46862, null, {"112": 14, "117": 80, "118": 402, "119": 785, "120": 150, "121": 10739, "100": 6, "101": 28, "102": 6265, "122": 1041, "111": 55484, "114": 16, "116": 15}, 0, 1747228897], [640, 47541, null, {"112": 18, "117": 103, "118": 116, "119": 82, "120": 118, "121": 35357, "100": 2, "101": 37, "102": 727, "122": 7612, "111": 35505, "114": 16, "116": 1}, 0, 1747229631], [640, 16348, null, {"112": 14, "117": 265, "118": 1, "119": 1192, "120": 17, "121": 10740, "100": 6, "101": 28, "102": 4126, "122": 1914, "111": 10197, "114": 9, "116": 15}, 0, 1747270589], [640, 38866, null, {"112": 14, "117": 80, "118": 160, "119": 458, "120": 205, "121": 22552, "100": 6, "101": 28, "102": 6610, "122": 341, "111": 11957, "114": 9, "116": 15}, 0, 1747277577], [640, 10747, null, {"112": 14, "117": 104, "118": 59, "119": 1649, "120": 161, "121": 33462, "100": 6, "101": 18, "102": 2476, "122": 9619, "111": 134, "114": 16, "115": 17, "116": 13}, 0, 1747278124], [640, 5620, null, {"112": 14, "117": 219, "118": 391, "119": 1467, "120": 118, "121": 11746, "100": 6, "101": 18, "102": 3824, "122": 3100, "111": 140, "114": 16, "116": 4}, 0, 1747280903], [640, 16092, null, {"112": 18, "117": 103, "118": 116, "119": 82, "120": 118, "121": 35357, "100": 2, "101": 37, "102": 727, "122": 7612, "111": 57052, "114": 16, "116": 1}, 0, 1747280949], [640, 34774, null, {"112": 14, "117": 80, "118": 249, "119": 1174, "120": 118, "121": 834, "100": 6, "101": 18, "102": 6554, "122": 8465, "111": 48555, "114": 16, "115": 88, "116": 13}, 0, 1747287100], [640, 50486, null, {"112": 14, "117": 190, "118": 194, "119": 1163, "120": 1239, "121": 30012, "100": 6, "101": 18, "102": 3267, "122": 628, "111": 32851, "114": 16, "116": 6}, 0, 1747287452], [640, 51810, null, {"112": 14, "117": 39, "118": 287, "119": 1100, "120": 118, "121": 29555, "100": 6, "101": 5, "102": 3959, "122": 10702, "111": 9147, "114": 16, "116": 6}, 0, 1747345839], [640, 9437, null, {"112": 14, "117": 80, "118": 107, "119": 393, "120": 398, "121": 37025, "100": 6, "101": 28, "102": 9377, "122": 2856, "111": 15504, "114": 16, "116": 15}, 0, 1747354054], [640, 27722, null, {"112": 14, "117": 50, "118": 478, "119": 994, "120": 646, "121": 48400, "100": 6, "101": 28, "102": 8223, "122": 7443, "111": 41045, "114": 4, "116": 15}, 0, 1747372408], [640, 41687, null, {"112": 14, "117": 50, "118": 478, "119": 883, "120": 88, "121": 40349, "100": 6, "101": 18, "102": 10562, "122": 5155, "111": 49222, "114": 16, "116": 6}, 0, 1747374990], [640, 44147, null, {"112": 14, "117": 190, "118": 194, "119": 1691, "120": 1151, "121": 47380, "100": 6, "101": 18, "102": 10763, "122": 6985, "111": 54387, "114": 16, "115": 44, "116": 13}, 0, 1747451533], [640, 13559, null, {"112": 14, "117": 190, "118": 194, "119": 157, "120": 666, "121": 41539, "100": 6, "101": 28, "102": 2892, "122": 3588, "111": 7946, "114": 16, "116": 15}, 0, 1747451556], [640, 15505, null, {"112": 14, "117": 104, "118": 309, "119": 1432, "120": 11, "121": 37431, "100": 6, "101": 5, "102": 2612, "122": 6733, "111": 38891, "114": 16, "115": 88, "116": 13}, 0, 1747452819], [640, 50086, null, {"112": 14, "117": 50, "118": 205, "119": 1500, "120": 137, "121": 46324, "100": 6, "101": 18, "102": 1728, "122": 11103, "111": 25653, "114": 9, "116": 6}, 0, 1747453908], [640, 52302, null, {"112": 14, "117": 219, "118": 391, "119": 444, "120": 118, "121": 36201, "100": 6, "101": 18, "102": 8225, "122": 5319, "111": 7394, "114": 16, "116": 4}, 0, 1747486182], [640, 10181, null, {"112": 14, "117": 29, "118": 495, "119": 1235, "120": 118, "121": 4573, "100": 6, "101": 28, "102": 374, "122": 5944, "111": 43905, "114": 16, "116": 15}, 0, 1747489046], [640, 30601, null, {"112": 14, "117": 80, "118": 104, "119": 296, "120": 118, "121": 9286, "100": 6, "101": 9, "102": 6517, "122": 2413, "111": 4817, "114": 16, "116": 1}, 0, 1747490576], [640, 20864, null, {"112": 14, "117": 80, "118": 249, "119": 1322, "120": 118, "121": 5189, "100": 6, "101": 28, "102": 4628, "122": 7064, "111": 42992, "114": 16, "116": 15}, 0, 1747538066], [640, 1598, null, {"112": 14, "117": 39, "118": 741, "119": 568, "120": 118, "121": 40288, "100": 6, "101": 18, "102": 5640, "122": 241, "111": 40550, "114": 16, "115": 88, "116": 13}, 0, 1747539612], [640, 32641, null, {"112": 14, "117": 238, "118": 62, "119": 116, "120": 11, "121": 41670, "100": 6, "101": 28, "102": 4499, "122": 1952, "111": 48318, "114": 4, "116": 15}, 0, 1747624227], [640, 48737, null, {"112": 14, "117": 265, "118": 689, "119": 1431, "120": 695, "121": 38035, "100": 6, "101": 28, "102": 137, "122": 1483, "111": 54388, "114": 16, "116": 15}, 0, 1747624900], [640, 7934, null, {"112": 14, "117": 83, "118": 175, "119": 856, "120": 1080, "121": 27929, "100": 6, "101": 28, "102": 3954, "122": 10128, "111": 16044, "114": 4, "116": 15}, 0, 1747625183], [640, 16838, null, {"121": 8276, "100": 6, "101": 28, "102": 2730, "122": 3304, "111": 54251, "114": 4, "116": 15}, 0, 1747637522], [640, 40619, null, {"112": 14, "117": 223, "118": 78, "119": 1405, "120": 204, "121": 12446, "100": 6, "101": 41, "102": 3246, "122": 2419, "111": 54389, "114": 16, "116": 4}, 0, 1747637644], [640, 1116, null, {"112": 14, "117": 80, "118": 568, "119": 1714, "120": 118, "121": 26093, "100": 6, "101": 28, "102": 4760, "122": 3611, "111": 5308, "114": 4, "116": 15}, 0, 1747637831], [640, 55076, null, {"112": 14, "117": 81, "118": 726, "119": 1581, "120": 397, "121": 35889, "100": 6, "101": 28, "102": 9360, "122": 6532, "111": 21787, "114": 16, "116": 15}, 0, 1747638079], [640, 22686, null, {"112": 14, "117": 81, "118": 431, "119": 1007, "120": 118, "121": 29172, "100": 6, "101": 28, "102": 1137, "122": 7390, "111": 35905, "114": 16, "116": 15}, 0, 1747721477], [640, 5648, null, {"112": 14, "117": 238, "118": 62, "119": 116, "120": 11, "121": 48722, "100": 6, "101": 28, "102": 26, "122": 8533, "111": 49223, "114": 16, "116": 15}, 0, 1747730686], [640, 8732, null, {"112": 14, "117": 80, "118": 249, "119": 1347, "120": 118, "121": 7938, "100": 6, "101": 28, "102": 9501, "122": 4609, "111": 14958, "114": 16, "116": 15}, 0, 1747732160], [640, 19202, null, {"112": 14, "117": 219, "118": 391, "119": 1467, "120": 118, "121": 22834, "100": 6, "101": 18, "102": 3824, "122": 3100, "111": 32971, "114": 16, "115": 51, "116": 13}, 0, 1747796891], [640, 43070, null, {"112": 14, "117": 264, "118": 477, "119": 1101, "120": 118, "121": 41540, "100": 6, "101": 28, "102": 3954, "122": 10128, "111": 14692, "114": 9, "116": 15}, 0, 1747797056], [640, 18889, null, {"112": 14, "117": 50, "118": 719, "119": 202, "120": 734, "121": 28130, "100": 6, "101": 28, "102": 2065, "122": 8871, "111": 17506, "114": 9, "116": 15}, 1, 1747813135], [640, 22039, null, {"112": 14, "117": 80, "118": 160, "119": 458, "120": 1005, "121": 5190, "100": 6, "101": 28, "102": 6610, "122": 341, "111": 10931, "114": 16, "116": 15}, 0, 1747814969], [640, 2837, null, {"112": 14, "117": 104, "118": 309, "119": 1358, "120": 118, "121": 41110, "100": 6, "101": 28, "102": 4936, "122": 5973, "111": 51970, "114": 9, "116": 15}, 0, 1747815221], [640, 48014, null, {"112": 14, "117": 50, "118": 291, "119": 579, "120": 414, "121": 37298, "100": 6, "101": 28, "102": 5322, "122": 7845, "111": 40361, "114": 4, "116": 15}, 0, 1747824168], [640, 14756, null, {"112": 14, "117": 181, "118": 318, "119": 365, "120": 247, "121": 2314, "100": 6, "101": 28, "102": 5313, "122": 3475, "111": 27446, "114": 4, "116": 15}, 0, 1747879442], [640, 15327, null, {"112": 14, "117": 50, "118": 667, "119": 1182, "120": 2, "121": 39972, "100": 6, "101": 28, "102": 4153, "122": 2540, "111": 14247, "114": 4, "116": 15}, 0, 1747899334], [640, 26930, null, {"112": 14, "117": 265, "118": 663, "119": 988, "120": 1100, "121": 32397, "100": 6, "101": 28, "102": 6193, "122": 10531, "111": 53129, "114": 16, "116": 15}, 0, 1747918350], [640, 33595, null, {"112": 14, "117": 80, "118": 104, "119": 1737, "120": 118, "121": 7939, "100": 6, "101": 9, "102": 8595, "122": 8176, "111": 35058, "114": 16, "116": 1}, 0, 1748000648], [640, null, {"103": 46, "106": [4], "109": 1, "105": 5, "104": 2}, null, null, 1748049465]]
    num1 = 0
    num2 = 0

    action_set = []
    for iD in d:
        if(iD[2] == "null"):
            num1+=1
        else:
            num2+=1
        if iD[-2] not in action_set:
            action_set.append(iD[-2])
    print(f"num1: {num1}, num2: {num2}")
    print(f"action set: {action_set}")

data = getPickleData(name='seq', mode='jsonl')
print(f"len: {len(data)}")
getSeqData(data)
# getSeqOffsetsData(data)
# getIndexerData(data)
