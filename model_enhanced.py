"""
增强版生成式推荐模型

主要改进：
1. 分层Transformer架构
2. 多尺度时序建模
3. 对比学习增强
4. 自适应负采样
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from model import FlashMultiHeadAttention, PointWiseFeedForward


class HierarchicalTransformerBlock(nn.Module):
    """分层Transformer块"""
    
    def __init__(self, hidden_units, num_heads, dropout_rate, norm_first=True):
        super().__init__()
        self.norm_first = norm_first
        
        # 局部注意力（短期兴趣）
        self.local_attention = FlashMultiHeadAttention(hidden_units, num_heads, dropout_rate)
        self.local_norm1 = nn.LayerNorm(hidden_units)
        self.local_norm2 = nn.LayerNorm(hidden_units)
        self.local_ffn = PointWiseFeedForward(hidden_units, dropout_rate)
        
        # 全局注意力（长期兴趣）
        self.global_attention = FlashMultiHeadAttention(hidden_units, num_heads, dropout_rate)
        self.global_norm1 = nn.LayerNorm(hidden_units)
        self.global_norm2 = nn.LayerNorm(hidden_units)
        self.global_ffn = PointWiseFeedForward(hidden_units, dropout_rate)
        
        # 兴趣融合门控
        self.fusion_gate = nn.Linear(hidden_units * 2, hidden_units)
        
    def forward(self, x, local_mask, global_mask):
        # 局部建模（最近K个交互）
        if self.norm_first:
            local_out, _ = self.local_attention(
                self.local_norm1(x), self.local_norm1(x), self.local_norm1(x), local_mask
            )
            x_local = x + local_out
            x_local = x_local + self.local_ffn(self.local_norm2(x_local))
        else:
            local_out, _ = self.local_attention(x, x, x, local_mask)
            x_local = self.local_norm1(x + local_out)
            x_local = self.local_norm2(x_local + self.local_ffn(x_local))
        
        # 全局建模（所有交互）
        if self.norm_first:
            global_out, _ = self.global_attention(
                self.global_norm1(x), self.global_norm1(x), self.global_norm1(x), global_mask
            )
            x_global = x + global_out
            x_global = x_global + self.global_ffn(self.global_norm2(x_global))
        else:
            global_out, _ = self.global_attention(x, x, x, global_mask)
            x_global = self.global_norm1(x + global_out)
            x_global = self.global_norm2(x_global + self.global_ffn(x_global))
        
        # 自适应融合
        fusion_input = torch.cat([x_local, x_global], dim=-1)
        gate = torch.sigmoid(self.fusion_gate(fusion_input))
        output = gate * x_local + (1 - gate) * x_global
        
        return output


class ContrastiveLearningHead(nn.Module):
    """对比学习头"""
    
    def __init__(self, hidden_units, temperature=0.1):
        super().__init__()
        self.temperature = temperature
        self.projection = nn.Sequential(
            nn.Linear(hidden_units, hidden_units),
            nn.ReLU(),
            nn.Linear(hidden_units, hidden_units // 2)
        )
    
    def forward(self, user_emb, pos_emb, neg_emb):
        # 投影到对比学习空间
        user_proj = F.normalize(self.projection(user_emb), dim=-1)
        pos_proj = F.normalize(self.projection(pos_emb), dim=-1)
        neg_proj = F.normalize(self.projection(neg_emb), dim=-1)
        
        # 计算相似度
        pos_sim = torch.sum(user_proj * pos_proj, dim=-1) / self.temperature
        neg_sim = torch.sum(user_proj * neg_proj, dim=-1) / self.temperature
        
        return pos_sim, neg_sim


class AdaptiveNegativeSampler(nn.Module):
    """自适应负采样器"""
    
    def __init__(self, item_num, hidden_units, num_negatives=5):
        super().__init__()
        self.item_num = item_num
        self.num_negatives = num_negatives
        
        # 学习item的流行度分布
        self.popularity_embedding = nn.Embedding(item_num + 1, 1)
        
        # 难度评估网络
        self.difficulty_net = nn.Sequential(
            nn.Linear(hidden_units, hidden_units // 2),
            nn.ReLU(),
            nn.Linear(hidden_units // 2, 1),
            nn.Sigmoid()
        )
    
    def forward(self, user_emb, pos_items, user_history):
        batch_size = user_emb.size(0)
        device = user_emb.device
        
        # 基于用户表征的难度评估
        difficulty_scores = self.difficulty_net(user_emb)  # [batch_size, 1]
        
        neg_items = []
        for i in range(batch_size):
            history_set = set(user_history[i].cpu().numpy())
            candidates = []
            
            # 混合采样策略
            for _ in range(self.num_negatives):
                if torch.rand(1) < difficulty_scores[i]:
                    # 困难负样本：基于流行度采样
                    popularity_scores = self.popularity_embedding.weight.squeeze()
                    probs = F.softmax(popularity_scores, dim=0)
                    neg_item = torch.multinomial(probs, 1).item()
                else:
                    # 简单负样本：随机采样
                    neg_item = torch.randint(1, self.item_num + 1, (1,)).item()
                
                # 确保不在用户历史中
                while neg_item in history_set:
                    neg_item = torch.randint(1, self.item_num + 1, (1,)).item()
                
                candidates.append(neg_item)
            
            neg_items.append(candidates)
        
        return torch.tensor(neg_items, device=device)


class EnhancedGenerativeModel(nn.Module):
    """增强版生成式推荐模型"""
    
    def __init__(self, user_num, item_num, feat_statistics, feat_types, args):
        super().__init__()
        
        # 基础组件（继承原有逻辑）
        self.user_num = user_num
        self.item_num = item_num
        self.dev = args.device
        self.hidden_units = args.hidden_units
        
        # Embedding层
        self.item_emb = nn.Embedding(item_num + 1, args.hidden_units, padding_idx=0)
        self.user_emb = nn.Embedding(user_num + 1, args.hidden_units, padding_idx=0)
        self.pos_emb = nn.Embedding(2 * args.maxlen + 1, args.hidden_units, padding_idx=0)
        
        # 分层Transformer
        self.hierarchical_blocks = nn.ModuleList([
            HierarchicalTransformerBlock(
                args.hidden_units, args.num_heads, args.dropout_rate, args.norm_first
            ) for _ in range(args.num_blocks)
        ])
        
        # 多尺度时序建模
        self.temporal_scales = [1, 3, 7]  # 不同时间窗口
        self.scale_projections = nn.ModuleList([
            nn.Linear(args.hidden_units, args.hidden_units) 
            for _ in self.temporal_scales
        ])
        self.scale_fusion = nn.Linear(
            args.hidden_units * len(self.temporal_scales), args.hidden_units
        )
        
        # 对比学习
        self.contrastive_head = ContrastiveLearningHead(args.hidden_units)
        
        # 自适应负采样
        self.adaptive_sampler = AdaptiveNegativeSampler(item_num, args.hidden_units)
        
        # 输出层
        self.final_norm = nn.LayerNorm(args.hidden_units)
        
        # 特征处理（保持原有逻辑）
        self._init_feat_components(feat_statistics, feat_types, args)
    
    def _init_feat_components(self, feat_statistics, feat_types, args):
        """初始化特征处理组件"""
        # 这里保持原有的特征处理逻辑
        # 包括sparse_emb, emb_transform, userdnn, itemdnn等
        pass
    
    def create_multi_scale_masks(self, seq_len, scales):
        """创建多尺度注意力掩码"""
        masks = []
        for scale in scales:
            # 创建局部窗口掩码
            mask = torch.zeros(seq_len, seq_len, dtype=torch.bool)
            for i in range(seq_len):
                start = max(0, i - scale + 1)
                mask[i, start:i+1] = True
            masks.append(mask)
        return masks
    
    def multi_scale_encoding(self, x, masks):
        """多尺度序列编码"""
        scale_outputs = []
        
        for i, (scale_proj, mask) in enumerate(zip(self.scale_projections, masks)):
            # 应用尺度特定的投影
            scale_x = scale_proj(x)
            
            # 使用对应尺度的掩码进行注意力计算
            for block in self.hierarchical_blocks:
                scale_x = block(scale_x, mask.unsqueeze(0), mask.unsqueeze(0))
            
            scale_outputs.append(scale_x)
        
        # 融合多尺度特征
        fused = torch.cat(scale_outputs, dim=-1)
        output = self.scale_fusion(fused)
        
        return output
    
    def forward(self, user_item, pos_seqs, neg_seqs, mask, next_mask, 
                next_action_type, seq_feature, pos_feature, neg_feature):
        """增强版前向传播"""
        
        # 1. 基础序列编码
        seq_emb = self.encode_sequence(user_item, mask, seq_feature)
        
        # 2. 多尺度时序建模
        seq_len = seq_emb.size(1)
        multi_scale_masks = self.create_multi_scale_masks(seq_len, self.temporal_scales)
        enhanced_seq = self.multi_scale_encoding(seq_emb, multi_scale_masks)
        
        # 3. 最终表征
        final_repr = self.final_norm(enhanced_seq)
        
        # 4. 正负样本编码
        pos_embs = self.encode_items(pos_seqs, pos_feature)
        neg_embs = self.encode_items(neg_seqs, neg_feature)
        
        # 5. 计算logits
        pos_logits = torch.sum(final_repr * pos_embs, dim=-1)
        neg_logits = torch.sum(final_repr * neg_embs, dim=-1)
        
        # 6. 对比学习增强
        user_repr = final_repr[:, -1, :]  # 最后一个位置的表征
        pos_repr = pos_embs[:, -1, :]
        neg_repr = neg_embs[:, -1, :]
        
        contrastive_pos, contrastive_neg = self.contrastive_head(
            user_repr, pos_repr, neg_repr
        )
        
        return pos_logits, neg_logits, contrastive_pos, contrastive_neg
    
    def encode_sequence(self, user_item, mask, seq_feature):
        """序列编码（简化版，实际需要完整实现）"""
        # 这里需要实现完整的序列编码逻辑
        # 包括embedding lookup, 位置编码等
        return self.item_emb(user_item)
    
    def encode_items(self, items, features):
        """物品编码（简化版，实际需要完整实现）"""
        # 这里需要实现完整的物品编码逻辑
        return self.item_emb(items)
