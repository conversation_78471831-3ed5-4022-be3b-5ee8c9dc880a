import data.processed

train.iterations=20000
train.batch_size=64
train.vae_input_dim=768
train.vae_hidden_dims=[512, 256, 128]
train.vae_embed_dim=64
train.vae_n_cat_feats=0
train.vae_codebook_size=256
train.wandb_logging=False
train.pretrained_rqvae_path="trained_models/rqvae_ml32m/checkpoint_high_entropy.pt"
train.save_dir_root="out/decoder/ml-32m/"
train.dataset_folder="dataset/ml-32m"
train.dataset=%data.processed.RecDataset.ML_32M
train.force_dataset_process=False
train.full_eval_every=5000
train.partial_eval_every=5000
train.dataset_split="beauty"
train.attn_dropout=0.1
train.attn_heads=6
train.attn_embed_dim=384
train.attn_layers=8
train.decoder_embed_dim=128