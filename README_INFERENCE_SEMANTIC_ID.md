# Semantic ID 推理代码使用指南

## 概述

本文档介绍如何使用支持Semantic ID的推理代码进行模型推理。该推理代码解决了高维embedding（83, 84, 85, 86）在推理时的内存不足问题。

## 核心思路

1. **加载RQ-VAE模型**: 加载训练好的RQ-VAE模型权重
2. **转换测试集特征**: 将测试集的高维embedding转换为Semantic ID
3. **加载主模型**: 加载训练好的主模型进行预测
4. **生成推荐结果**: 使用转换后的特征进行推理和ANN检索

## 文件结构

```
├── infer_with_semantic_id.py           # 核心推理代码
├── run_inference_with_semantic_id.py   # 推理运行脚本
├── dataset_with_semantic_id.py         # 支持Semantic ID的数据集（已扩展测试集支持）
└── README_INFERENCE_SEMANTIC_ID.md     # 本文档
```

## 前置条件

### 1. 已训练的RQ-VAE模型

确保已经训练好RQ-VAE模型，模型文件应位于 `rqvae_models/` 目录：

```
rqvae_models/
├── rqvae_feat_83_best.pt
├── rqvae_feat_84_best.pt
├── rqvae_feat_85_best.pt
└── rqvae_feat_86_best.pt
```

### 2. 已训练的主模型

确保已经训练好支持Semantic ID的主模型，模型检查点应位于环境变量 `MODEL_OUTPUT_PATH` 指定的目录。

### 3. 测试数据

确保测试数据位于环境变量 `EVAL_DATA_PATH` 指定的目录，包含：
- `predict_seq.jsonl`: 用户序列数据
- `predict_seq_offsets.pkl`: 序列偏移量
- `predict_set.jsonl`: 候选集数据
- `creative_emb/`: 多模态embedding数据

## 使用方法

### 方法1: 使用运行脚本（推荐）

```bash
# 基本使用
python run_inference_with_semantic_id.py \
    --semantic_id_features 83 84 85 86 \
    --mm_emb_id 81

# 自定义参数
python run_inference_with_semantic_id.py \
    --semantic_id_features 83 84 \
    --mm_emb_id 81 82 \
    --batch_size 64 \
    --device cuda \
    --save_results \
    --output_file my_inference_results.json
```

### 方法2: 直接调用推理函数

```python
import os
from infer_with_semantic_id import infer_with_semantic_id

# 设置环境变量
os.environ['EVAL_DATA_PATH'] = './data/TencentGR_1k'
os.environ['EVAL_RESULT_PATH'] = './inference_results'
os.environ['MODEL_OUTPUT_PATH'] = './checkpoints'

# 运行推理
top10s, user_list = infer_with_semantic_id()
```

### 方法3: 设置命令行参数

```bash
python infer_with_semantic_id.py \
    --semantic_id_features 83 84 85 86 \
    --mm_emb_id 81 \
    --rqvae_model_dir rqvae_models \
    --batch_size 128 \
    --device cuda
```

## 参数说明

### 核心参数

- `--semantic_id_features`: 要转换为Semantic ID的特征列表（如 83 84 85 86）
- `--mm_emb_id`: 原始多模态特征列表（建议只用低维度的81）
- `--rqvae_model_dir`: RQ-VAE模型目录路径

### 模型参数

- `--batch_size`: 推理批次大小（默认128）
- `--device`: 计算设备（cuda/cpu）
- `--hidden_units`: 隐藏层维度（需与训练时一致）
- `--num_blocks`: Transformer块数量（需与训练时一致）

### 环境变量

- `EVAL_DATA_PATH`: 测试数据路径
- `EVAL_RESULT_PATH`: 推理结果输出路径
- `MODEL_OUTPUT_PATH`: 训练好的模型路径

## 推理流程详解

### 1. 模型加载阶段

```python
# 加载RQ-VAE模型
rqvae_models = load_rqvae_models(args.rqvae_model_dir, args.semantic_id_features, args.device)

# 加载多模态embedding
mm_emb_dict = load_mm_emb(Path(data_path, "creative_emb"), all_mm_features)
```

### 2. Semantic ID转换阶段

```python
# 为候选集生成Semantic ID
semantic_id_mappings = generate_semantic_ids_for_candidates(
    candidate_path, rqvae_models, args.semantic_id_features, mm_emb_dict
)
```

### 3. 数据集创建阶段

```python
# 创建支持Semantic ID的测试数据集
test_dataset = MyTestDatasetWithSemanticID(data_path, args)
```

### 4. 模型推理阶段

```python
# 加载主模型并进行推理
model = BaselineModel(usernum, itemnum, feat_statistics, feat_types, args)
model.load_state_dict(torch.load(ckpt_path))

# 生成用户表征
for batch in test_loader:
    logits = model.predict(seq, seq_feat, token_type)
```

### 5. 候选集处理阶段

```python
# 生成候选集embedding（包含Semantic ID特征）
retrieve_id2creative_id = get_candidate_emb_with_semantic_id(
    indexer, feat_types, feat_default_value, mm_emb_dict, 
    model, semantic_id_mappings, semantic_id_indexer
)
```

### 6. ANN检索阶段

```python
# 使用faiss进行近似最近邻检索
os.system(ann_cmd)
top10s_retrieved = read_result_ids(result_file)
```

## 输出结果

### 推理结果格式

```python
# 返回值
top10s: List[List[str]]  # 每个用户的top-10推荐结果
user_list: List[str]     # 用户ID列表

# 示例
top10s = [
    ['creative_123', 'creative_456', ...],  # 用户1的推荐
    ['creative_789', 'creative_012', ...],  # 用户2的推荐
]
user_list = ['user_001', 'user_002', ...]
```

### 生成的文件

```
inference_results/
├── query.fbin                    # 用户查询向量
├── embedding.fbin                # 候选集embedding
├── id.u64bin                     # 候选集ID映射
├── id100.u64bin                  # ANN检索结果
└── retrive_id2creative_id.json   # 检索ID到创意ID的映射
```

## 内存优化效果

| 组件 | 原始方案 | Semantic ID方案 | 内存节省 |
|------|----------|----------------|----------|
| 特征83 | 3584维float32 | 离散ID | ~99% |
| 特征84 | 4096维float32 | 离散ID | ~99% |
| 特征85 | 3584维float32 | 离散ID | ~99% |
| 特征86 | 3584维float32 | 离散ID | ~99% |

## 故障排除

### 1. RQ-VAE模型不存在

```bash
# 错误信息
警告: RQ-VAE模型文件不存在: rqvae_models/rqvae_feat_83_best.pt

# 解决方案
python train_rqvae_compress.py --feature_id 83 --num_epochs 20
```

### 2. 内存不足

```bash
# 减少批次大小
python run_inference_with_semantic_id.py --batch_size 64

# 只使用部分特征
python run_inference_with_semantic_id.py --semantic_id_features 83 84
```

### 3. 模型检查点不存在

```bash
# 错误信息
ValueError: MODEL_OUTPUT_PATH is not set

# 解决方案
export MODEL_OUTPUT_PATH=./checkpoints
# 或在脚本中设置
os.environ['MODEL_OUTPUT_PATH'] = './checkpoints'
```

## 性能对比

| 指标 | 原始推理 | Semantic ID推理 |
|------|----------|----------------|
| 内存使用 | 19GB+ | <10GB |
| 推理速度 | 基准 | 相近或更快 |
| 推荐效果 | 基准 | 预期相近 |

## 最佳实践

1. **特征选择**: 优先使用83、84等高维特征作为Semantic ID
2. **批次大小**: 根据GPU内存调整，建议64-128
3. **模型组合**: 保留81等低维特征作为原始embedding
4. **结果验证**: 对比原始推理结果验证效果

## 扩展功能

该推理代码支持：
- 动态特征组合
- 批量处理
- 结果缓存
- 错误恢复
- 性能监控

通过这套推理代码，您可以在资源受限的环境中成功运行支持高维多模态特征的推荐模型。
