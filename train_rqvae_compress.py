#!/usr/bin/env python3
"""
RQ-VAE训练脚本：将高维多模态embedding压缩为Semantic ID

使用流程：
1. 训练RQ-VAE模型压缩高维embedding
2. 生成Semantic ID映射文件
3. 在主模型中使用Semantic ID作为稀疏特征

作者：腾讯广告算法大赛参赛者
"""

import os
import json
import pickle
import argparse
from pathlib import Path
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm

from model_rqvae import MmEmbDataset, RQVAE


def get_args():
    parser = argparse.ArgumentParser(description='RQ-VAE Training for High-Dim Embedding Compression')
    
    # 数据参数
    parser.add_argument('--data_dir', default='data/TencentGR_1k', type=str, help='数据目录')
    parser.add_argument('--feature_id', default='83', type=str, 
                       help='要压缩的多模态特征ID')
    
    # RQ-VAE模型参数
    parser.add_argument('--latent_dim', default=64, type=int, help='潜在空间维度')
    parser.add_argument('--hidden_channels', default=[512, 256], type=list, help='隐藏层维度')
    parser.add_argument('--num_codebooks', default=2, type=int, help='码本数量')
    # parser.add_argument('--codebook_size', nargs='+', type=int, default=[256, 256], help='每个码本大小')
    parser.add_argument('--codebook_size', type=int, default=256, help='每个码本大小')
    parser.add_argument('--shared_codebook', default=False, type=bool, help='是否共享码本')
    
    # 训练参数
    parser.add_argument('--batch_size', default=512, type=int, help='批次大小')
    parser.add_argument('--lr', default=1e-3, type=float, help='学习率')
    parser.add_argument('--num_epochs', default=50, type=int, help='训练轮数')
    parser.add_argument('--device', default='cuda', type=str, help='设备')
    
    # K-means参数
    parser.add_argument('--kmeans_method', default='kmeans', type=str, choices=['kmeans', 'bkmeans'])
    parser.add_argument('--kmeans_iters', default=100, type=int)
    parser.add_argument('--distances_method', default='l2', type=str, choices=['l2', 'cosine'])
    parser.add_argument('--loss_beta', default=0.25, type=float)
    
    # 输出参数
    parser.add_argument('--output_dir', default='rqvae_models', type=str, help='模型保存目录')
    parser.add_argument('--semantic_id_dir', default='semantic_ids', type=str, help='Semantic ID保存目录')
    
    return parser.parse_args()


def train_rqvae(args):
    """训练RQ-VAE模型"""
    print(f"开始训练RQ-VAE模型，压缩特征ID: {args.feature_id}")
    
    # 创建输出目录
    Path(args.output_dir).mkdir(parents=True, exist_ok=True)
    Path(args.semantic_id_dir).mkdir(parents=True, exist_ok=True)
    
    # 加载数据集
    print("加载多模态embedding数据...")
    dataset = MmEmbDataset(args.data_dir, args.feature_id)
    dataloader = DataLoader(dataset, batch_size=args.batch_size, shuffle=True, 
                           collate_fn=dataset.collate_fn, num_workers=0, drop_last=True)
    
    # 获取输入维度
    input_dim = dataset.emb_list[0].shape[0]
    print(f"输入维度: {input_dim}, 数据量: {len(dataset)}")
    
    # 创建RQ-VAE模型
    model = RQVAE(
        input_dim=input_dim,
        hidden_channels=args.hidden_channels,
        latent_dim=args.latent_dim,
        num_codebooks=args.num_codebooks,
        codebook_size=args.codebook_size,
        shared_codebook=args.shared_codebook,
        kmeans_method=args.kmeans_method,
        kmeans_iters=args.kmeans_iters,
        distances_method=args.distances_method,
        loss_beta=args.loss_beta,
        device=args.device
    )
    
    # 优化器
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)
    
    # 训练循环
    model.train()
    best_loss = float('inf')
    
    for epoch in range(args.num_epochs):
        epoch_loss = 0.0
        epoch_recon_loss = 0.0
        epoch_rqvae_loss = 0.0
        
        pbar = tqdm(dataloader, desc=f'Epoch {epoch+1}/{args.num_epochs}')
        for batch_idx, (tid_batch, emb_batch) in enumerate(pbar):
            emb_batch = emb_batch.to(args.device)
            
            # 前向传播
            x_hat, semantic_id_list, recon_loss, rqvae_loss, total_loss = model(emb_batch)
            
            # 反向传播
            optimizer.zero_grad()
            total_loss.backward()
            optimizer.step()
            
            # 记录损失
            epoch_loss += total_loss.item()
            epoch_recon_loss += recon_loss.item()
            epoch_rqvae_loss += rqvae_loss.item()
            
            # 更新进度条
            pbar.set_postfix({
                'Total': f'{total_loss.item():.4f}',
                'Recon': f'{recon_loss.item():.4f}',
                'RQ': f'{rqvae_loss.item():.4f}'
            })
        
        # 计算平均损失
        avg_loss = epoch_loss / len(dataloader)
        avg_recon_loss = epoch_recon_loss / len(dataloader)
        avg_rqvae_loss = epoch_rqvae_loss / len(dataloader)
        
        print(f'Epoch {epoch+1}: Loss={avg_loss:.4f}, Recon={avg_recon_loss:.4f}, RQ={avg_rqvae_loss:.4f}')
        
        # 保存最佳模型
        if avg_loss < best_loss:
            best_loss = avg_loss
            save_dir_rqvae = Path(args.output_dir) / f"global_step_best"
            model_path = save_dir_rqvae / f'rqvae_feat_{args.feature_id}_best.pt'
            save_dir_rqvae.mkdir(parents=True, exist_ok=True)
            torch.save({
                'model_state_dict': model.state_dict(),
                'args': args,
                'epoch': epoch,
                'loss': avg_loss
            }, model_path)
            print(f'保存最佳模型到: {model_path}')
    
    return model


def generate_semantic_ids(model, args):
    """生成Semantic ID映射"""
    print(f"生成特征ID {args.feature_id} 的Semantic ID映射...")
    
    # 加载数据集
    dataset = MmEmbDataset(args.data_dir, args.feature_id)
    dataloader = DataLoader(dataset, batch_size=args.batch_size, shuffle=False, 
                           collate_fn=dataset.collate_fn, num_workers=0, drop_last=True)
    
    model.eval()
    semantic_id_mapping = {}
    
    with torch.no_grad():
        for tid_batch, emb_batch in tqdm(dataloader, desc='生成Semantic ID'):
            emb_batch = emb_batch.to(args.device)
            
            # 获取Semantic ID
            semantic_ids = model._get_codebook(emb_batch)  # [batch_size, num_codebooks]
            
            # 转换为字符串形式的ID（用于作为稀疏特征）
            for i, tid in enumerate(tid_batch):
                tid_str = str(tid.item())
                # 将多个codebook的ID连接成一个字符串
                semantic_id_str = '_'.join([str(sid.item()) for sid in semantic_ids[i]])
                semantic_id_mapping[tid_str] = semantic_id_str
    
    # 保存映射文件
    mapping_path = Path(args.semantic_id_dir) / f'semantic_id_mapping_feat_{args.feature_id}.json'
    with open(mapping_path, 'w') as f:
        json.dump(semantic_id_mapping, f, indent=2)
    
    print(f'Semantic ID映射保存到: {mapping_path}')
    print(f'映射数量: {len(semantic_id_mapping)}')
    
    return semantic_id_mapping


def main():
    args = get_args()
    # args.data_dir = os.environ['TRAIN_DATA_PATH']
    # if 'TRAIN_CKPT_PATH' not in os.environ:
    #     os.environ['TRAIN_CKPT_PATH'] = './checkpoints'
    # args.output_dir = os.environ.get('TRAIN_CKPT_PATH')
    t = args.codebook_size
    args.hidden_channels[-1] = t
    args.codebook_size = [t for i in range(args.num_codebooks)]
    # args.semantic_id_dir = os.path.join(args.data_dir, args.semantic_id_dir)
    # args.output_dir = os.path.join(args.data_dir, args.output_dir)
    
    print("=" * 60)
    print("RQ-VAE高维Embedding压缩训练")
    print("=" * 60)
    print(f"特征ID: {args.feature_id}")
    print(f"设备: {args.device}")
    print(f"批次大小: {args.batch_size}")
    print(f"训练轮数: {args.num_epochs}")
    print("=" * 60)
    
    # 训练RQ-VAE模型
    model = train_rqvae(args)
    
    # 生成Semantic ID映射
    semantic_id_mapping = generate_semantic_ids(model, args)
    
    print("=" * 60)
    print("RQ-VAE训练完成！")
    print(f"模型保存在: {args.output_dir}")
    print(f"Semantic ID映射保存在: {args.semantic_id_dir}")
    print("=" * 60)


if __name__ == '__main__':
    main()
