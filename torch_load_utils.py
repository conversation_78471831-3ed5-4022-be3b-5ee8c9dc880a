"""
安全的torch.load包装函数

解决PyTorch 2.6 weights_only默认值变更问题
"""

import torch
import warnings
from pathlib import Path


def safe_torch_load(file_path, map_location=None, **kwargs):
    """
    安全的torch.load包装函数
    
    Args:
        file_path: 模型文件路径
        map_location: 设备映射
        **kwargs: 其他参数
    
    Returns:
        加载的模型数据
    """
    try:
        # 首先尝试使用weights_only=False
        return torch.load(file_path, map_location=map_location, weights_only=False, **kwargs)
    except Exception as e:
        # 如果失败，尝试其他方法
        warnings.warn(f"使用weights_only=False加载失败: {e}")
        
        try:
            # 尝试不指定weights_only参数
            return torch.load(file_path, map_location=map_location, **kwargs)
        except Exception as e2:
            # 如果还是失败，抛出原始错误
            raise e


def load_rqvae_checkpoint(model_path, device):
    """
    专门用于加载RQ-VAE模型的函数
    
    Args:
        model_path: 模型文件路径
        device: 目标设备
    
    Returns:
        checkpoint: 加载的检查点数据
    """
    print(f"正在加载RQ-VAE模型: {model_path}")
    
    if not Path(model_path).exists():
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    checkpoint = safe_torch_load(model_path, map_location=device)
    
    # 处理不同的保存格式
    if isinstance(checkpoint, dict):
        if 'model_state_dict' in checkpoint:
            print("检测到完整checkpoint格式 (包含model_state_dict)")
            return checkpoint
        elif 'model' in checkpoint:
            print("检测到标准checkpoint格式 (包含model)")
            return checkpoint
        else:
            # 假设整个字典就是state_dict
            print("检测到直接state_dict格式")
            return {'model_state_dict': checkpoint}
    else:
        # 假设直接是state_dict
        print("检测到原始state_dict格式")
        return {'model_state_dict': checkpoint}


def load_model_state_dict(model, checkpoint):
    """
    安全地加载模型状态字典
    
    Args:
        model: PyTorch模型
        checkpoint: 检查点数据
    """
    if isinstance(checkpoint, dict):
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            print("成功加载model_state_dict")
        elif 'model' in checkpoint:
            model.load_state_dict(checkpoint['model'])
            print("成功加载model")
        else:
            model.load_state_dict(checkpoint)
            print("成功加载checkpoint作为state_dict")
    else:
        model.load_state_dict(checkpoint)
        print("成功加载原始state_dict")


def load_rqvae_model(model, model_path, device):
    """
    完整的RQ-VAE模型加载流程
    
    Args:
        model: RQ-VAE模型实例
        model_path: 模型文件路径
        device: 目标设备
    
    Returns:
        model: 加载权重后的模型
    """
    try:
        # 加载checkpoint
        checkpoint = load_rqvae_checkpoint(model_path, device)
        
        # 加载模型权重
        load_model_state_dict(model, checkpoint)
        
        # 设置为评估模式
        model.eval()
        
        print(f"✅ RQ-VAE模型加载成功: {model_path}")
        
        # 返回额外信息（如果有的话）
        extra_info = {}
        if isinstance(checkpoint, dict):
            if 'args' in checkpoint:
                extra_info['args'] = checkpoint['args']
            if 'epoch' in checkpoint:
                extra_info['epoch'] = checkpoint['epoch']
            if 'loss' in checkpoint:
                extra_info['loss'] = checkpoint['loss']
        
        return model, extra_info
        
    except Exception as e:
        print(f"❌ RQ-VAE模型加载失败: {e}")
        raise


def create_rqvae_model_from_checkpoint(model_path, device, model_class=None):
    """
    从checkpoint创建RQ-VAE模型（如果checkpoint包含模型配置）
    
    Args:
        model_path: 模型文件路径
        device: 目标设备
        model_class: 模型类（如果需要手动指定）
    
    Returns:
        model: 创建并加载权重的模型
        extra_info: 额外信息
    """
    checkpoint = load_rqvae_checkpoint(model_path, device)
    
    # 尝试从checkpoint获取模型配置
    if isinstance(checkpoint, dict) and 'args' in checkpoint:
        args = checkpoint['args']
        
        # 如果有模型类，使用配置创建模型
        if model_class is not None:
            # 从args中提取模型参数
            model_params = {
                'input_dim': getattr(args, 'input_dim', None),
                'hidden_channels': getattr(args, 'hidden_channels', [512, 256]),
                'latent_dim': getattr(args, 'latent_dim', 64),
                'num_codebooks': getattr(args, 'num_codebooks', 2),
                'codebook_size': getattr(args, 'codebook_size', [256, 256]),
                'shared_codebook': getattr(args, 'shared_codebook', False),
                'kmeans_method': getattr(args, 'kmeans_method', 'kmeans'),
                'kmeans_iters': getattr(args, 'kmeans_iters', 50),
                'distances_method': getattr(args, 'distances_method', 'l2'),
                'loss_beta': getattr(args, 'loss_beta', 0.25),
                'device': device
            }
            
            # 过滤None值
            model_params = {k: v for k, v in model_params.items() if v is not None}
            
            # 创建模型
            model = model_class(**model_params)
            
            # 加载权重
            load_model_state_dict(model, checkpoint)
            
            # 设置为评估模式
            model.eval()
            
            extra_info = {
                'args': args,
                'epoch': checkpoint.get('epoch', None),
                'loss': checkpoint.get('loss', None)
            }
            
            return model, extra_info
    
    raise ValueError("无法从checkpoint创建模型，请手动创建模型后使用load_rqvae_model函数")


# 向后兼容的函数名
def load_checkpoint_safe(model_path, device):
    """向后兼容的函数名"""
    return load_rqvae_checkpoint(model_path, device)


def load_model_safe(model, checkpoint):
    """向后兼容的函数名"""
    return load_model_state_dict(model, checkpoint)
