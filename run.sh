#!/bin/bash

# show ${RUNTIME_SCRIPT_DIR}
echo ${RUNTIME_SCRIPT_DIR}
# enter train workspace
cd ${RUNTIME_SCRIPT_DIR}

# write your code below
# python -u main.py
# python main_with_semantic_id.py --semantic_id_features 83 --mm_emb_id 81 --num_epochs 3 --experiment_name my_semantic_experiment
# python run_semantic_id_pipeline.py --features 82 --rqvae_epochs 1 --rqvae_batch_size 512 --main_epochs 3 --device cuda
python run_semantic_id_pipeline.py --features 82 --rqvae_epochs 15 --rqvae_batch_size 512 --main_epochs 3 --device cuda --num_codebooks 2 --codebook_size 256