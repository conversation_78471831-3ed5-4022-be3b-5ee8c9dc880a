import data.processed
import modules.model

train.iterations=200000
train.learning_rate=0.0003
train.weight_decay=0.035
train.batch_size=256
train.vae_input_dim=768
train.vae_hidden_dims=[512, 256, 128]
train.vae_embed_dim=32  
train.vae_n_cat_feats=0
train.vae_codebook_size=256
train.wandb_logging=True
# https://api.wandb.ai/links/botta-edoardo-carnegie-mellon-university/ilggivkz, https://api.wandb.ai/links/botta-edoardo-carnegie-mellon-university/ufe656js
train.pretrained_rqvae_path="trained_models/rqvae_amazon_beauty/checkpoint_high_entropy.pt"
# Best run: https://api.wandb.ai/links/botta-edoardo-carnegie-mellon-university/xb46itxc
# Checkpoint run: https://api.wandb.ai/links/botta-edoardo-carnegie-mellon-university/6m2fkl1y
# train.pretrained_decoder_path="trained_models/transformer_amazon_beauty/checkpoint_199999.pt"
train.save_dir_root="out/decoder/amazon/"
train.dataset_folder="dataset/amazon"
train.dataset=%data.processed.RecDataset.AMAZON
train.force_dataset_process=False
train.full_eval_every=10000
train.partial_eval_every=5000
train.dataset_split="beauty"
train.dropout_p=0.3
train.attn_heads=8
train.attn_embed_dim=512
train.attn_layers=8
train.decoder_embed_dim=128
train.model_jagged_mode=True