# RQ-VAE Semantic ID 使用指南

## 问题背景

在腾讯广告算法大赛中，由于平台资源限制（19GiB显存、55GiB内存），直接加载高维多模态embedding（83: 3584维，84: 4096维，85: 3584维，86: 3584维）会导致内存不足错误：

```
2025-08-05 16:28:25.815 Loading mm_emb: 0%| | 0/1 [00:00<?, ?it/s]
/home/<USER>/dl/runtime/script/run.sh: line 9: 332 Killed python -u main.py
```

## 解决方案

使用RQ-VAE（Residual Quantized Variational AutoEncoder）将高维embedding压缩为低维的Semantic ID，然后作为稀疏特征加入模型训练。

### 核心思路

1. **压缩阶段**：使用RQ-VAE将高维embedding（如3584维）压缩为Semantic ID（如256个离散ID）
2. **映射阶段**：为每个item建立原始ID到Semantic ID的映射关系
3. **训练阶段**：将Semantic ID作为新的稀疏特征加入baseline模型

## 文件结构

```
├── model_rqvae.py                    # RQ-VAE模型实现（比赛方提供）
├── train_rqvae_compress.py           # RQ-VAE训练脚本
├── dataset_with_semantic_id.py       # 支持Semantic ID的数据集
├── main_with_semantic_id.py          # 支持Semantic ID的主训练脚本
├── run_semantic_id_pipeline.py       # 完整流程脚本
└── README_SEMANTIC_ID.md             # 使用说明（本文件）
```

## 快速开始

### 方法1：使用完整流程脚本（推荐）

```bash
# 处理特征83和84，使用默认参数
python run_semantic_id_pipeline.py --features 83 84

# 自定义参数
python run_semantic_id_pipeline.py \
    --features 83 84 85 86 \
    --rqvae_epochs 30 \
    --rqvae_batch_size 256 \
    --main_epochs 5 \
    --device cuda
```

### 方法2：分步执行

#### 步骤1：训练RQ-VAE模型

```bash
# 为特征83训练RQ-VAE
python train_rqvae_compress.py --feature_id 83 --num_epochs 20

# 为特征84训练RQ-VAE
python train_rqvae_compress.py --feature_id 84 --num_epochs 20
```

#### 步骤2：训练主模型

```bash
python main_with_semantic_id.py \
    --semantic_id_features 83 84 \
    --mm_emb_id 81 \
    --num_epochs 3 \
    --experiment_name my_semantic_experiment
```

## 参数说明

### RQ-VAE训练参数

- `--feature_id`: 要压缩的特征ID（83, 84, 85, 86）
- `--latent_dim`: 潜在空间维度（默认64）
- `--num_codebooks`: 码本数量（默认2）
- `--codebook_size`: 每个码本大小（默认[256, 256]）
- `--num_epochs`: 训练轮数（默认50）
- `--batch_size`: 批次大小（默认512）

### 主模型训练参数

- `--semantic_id_features`: 使用的Semantic ID特征列表
- `--mm_emb_id`: 原始多模态特征（建议只用81，低维度）
- `--experiment_name`: 实验名称，用于区分不同实验

## 内存优化效果

| 特征类型 | 原始维度 | 压缩后 | 内存节省 |
|---------|---------|--------|----------|
| 特征83  | 3584维  | 离散ID | ~99% |
| 特征84  | 4096维  | 离散ID | ~99% |
| 特征85  | 3584维  | 离散ID | ~99% |
| 特征86  | 3584维  | 离散ID | ~99% |

## 工作原理

### 1. RQ-VAE压缩过程

```
高维embedding (3584维) 
    ↓ Encoder
潜在表示 (64维)
    ↓ Residual Quantization
Semantic ID (如 "128_45")
    ↓ Decoder
重构embedding (3584维)
```

### 2. 特征集成过程

```python
# 原始方式（内存不足）
item_features = {
    '83': np.array([0.1, 0.2, ..., 0.3584])  # 3584维向量
}

# Semantic ID方式（内存友好）
item_features = {
    'semantic_83': 1234  # 离散ID，作为稀疏特征
}
```

## 输出文件

### RQ-VAE模型文件
- `rqvae_models/rqvae_feat_83_best.pt`: 特征83的RQ-VAE模型
- `rqvae_models/rqvae_feat_84_best.pt`: 特征84的RQ-VAE模型

### Semantic ID映射文件
- `semantic_ids/semantic_id_mapping_feat_83.json`: 特征83的ID映射
- `semantic_ids/semantic_id_indexer.pkl`: Semantic ID索引器

### 训练结果
- `checkpoints/semantic_id_83_84/`: 使用Semantic ID的模型检查点
- `logs/baseline_with_semantic_id/`: 训练日志
- `tf_events/baseline_with_semantic_id/`: TensorBoard事件文件

## 性能预期

1. **内存使用**：相比直接加载高维embedding，内存使用减少90%+
2. **训练速度**：由于特征维度降低，训练速度可能提升
3. **模型效果**：理论上应该接近或超过原始高维特征的效果

## 故障排除

### 1. 内存不足
```bash
# 减少批次大小
python run_semantic_id_pipeline.py --rqvae_batch_size 256 --main_batch_size 64
```

### 2. RQ-VAE训练失败
```bash
# 检查数据文件是否存在
ls data/TencentGR_1k/creative_emb/emb_83_3584/

# 使用更小的模型
python train_rqvae_compress.py --latent_dim 32 --hidden_channels [256, 128]
```

### 3. 跳过已训练的RQ-VAE
```bash
python run_semantic_id_pipeline.py --skip_rqvae --features 83 84
```

## 实验建议

1. **先用单个特征测试**：`--features 83`
2. **逐步增加特征**：`--features 83 84`，然后`--features 83 84 85 86`
3. **调整RQ-VAE参数**：尝试不同的`codebook_size`和`num_codebooks`
4. **对比实验**：记录使用不同特征组合的效果

## 技术细节

这个方案基于论文《Recommender Systems with Generative Retrieval》的思想，使用向量量化技术将连续的embedding空间离散化为有限的语义ID空间，既保持了语义信息又大幅减少了内存占用。
