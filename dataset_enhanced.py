"""
增强版数据集处理

主要改进：
1. 意图感知的序列构建
2. 多层次时序建模
3. 动态负采样策略
4. 行为类型感知
"""

import numpy as np
import torch
from dataset import MyDataset
from collections import defaultdict
import random


class IntentAwareDataset(MyDataset):
    """意图感知数据集"""
    
    def __init__(self, data_dir, args):
        super().__init__(data_dir, args)
        
        # 意图建模参数
        self.intent_window = getattr(args, 'intent_window', 10)
        self.behavior_types = ['view', 'click', 'purchase', 'share']
        
        # 构建用户意图图谱
        self._build_user_intent_graph()
        
        # 动态负采样器
        self.dynamic_sampler = DynamicNegativeSampler(
            self.itemnum, self.item_feat_dict, args
        )
    
    def _build_user_intent_graph(self):
        """构建用户意图演化图谱"""
        self.user_intent_graph = defaultdict(list)
        
        print("构建用户意图图谱...")
        for uid in range(len(self.seq_offsets)):
            user_sequence = self._load_user_data(uid)
            
            # 提取意图会话
            intent_sessions = self._extract_intent_sessions(user_sequence)
            self.user_intent_graph[uid] = intent_sessions
    
    def _extract_intent_sessions(self, user_sequence):
        """提取用户意图会话"""
        sessions = []
        current_session = []
        last_timestamp = 0

        u, i, user_feat, item_feat, action_type, timestamp = user_sequence[0]
        begin_time = timestamp
        for record in user_sequence:
            u, i, user_feat, item_feat, action_type, timestamp = record
            
            # 基于时间间隔和行为类型划分会话
            if (timestamp - last_timestamp > 3600) or len(current_session) >= self.intent_window:
                if current_session:
                    sessions.append({
                        'items': [r[1] for r in current_session if r[1]],
                        'actions': [r[4] for r in current_session if r[4] is not None],
                        'intent_type': self._infer_intent_type(current_session),
                        'start_time': begin_time
                    })
                current_session = []
                begin_time = timestamp
            
            current_session.append(record)
            last_timestamp = timestamp
        
        # 添加最后一个会话
        if current_session:
            sessions.append({
                'items': [r[1] for r in current_session if r[1]],
                'actions': [r[4] for r in current_session if r[4] is not None],
                'intent_type': self._infer_intent_type(current_session)
            })
        
        return sessions
    
    def _infer_intent_type(self, session):
        """推断会话意图类型"""
        actions = [r[4] for r in session if r[4] is not None]
        
        if not actions:
            return 'exploration'
        
        # 基于行为序列推断意图
        if 1 in actions:  # 包含点击
            if len(set([r[1] for r in session if r[1]])) == 1:
                return 'focused'  # 专注浏览
            else:
                return 'comparison'  # 对比浏览
        else:
            return 'exploration'  # 探索浏览
    
    def _build_hierarchical_sequence(self, user_sequence):
        """构建层次化序列表示"""
        # 1. 原始序列（item级别）
        item_sequence = []
        
        # 2. 会话序列（session级别）
        session_sequence = []
        
        # 3. 意图序列（intent级别）
        intent_sequence = []
        
        ext_user_sequence = []
        for record_tuple in user_sequence:
            u, i, user_feat, item_feat, action_type, timestamp = record_tuple
            
            # 用户特征处理
            if u and user_feat:
                ext_user_sequence.insert(0, (u, user_feat, 2, action_type, timestamp))
            
            # 物品特征处理
            if i and item_feat:
                ext_user_sequence.append((i, item_feat, 1, action_type, timestamp))
                item_sequence.append({
                    'item_id': i,
                    'features': item_feat,
                    'action_type': action_type,
                    'timestamp': timestamp
                })
        
        return ext_user_sequence, item_sequence
    
    def _create_multi_level_targets(self, sequence, idx):
        """创建多层次预测目标"""
        targets = {}
        
        if idx < len(sequence) - 1:
            # 下一个item预测
            targets['next_item'] = sequence[idx + 1]
            
            # 下一个行为类型预测
            if 'action_type' in sequence[idx + 1]:
                targets['next_action'] = sequence[idx + 1]['action_type']
            
            # 会话结束预测
            if idx < len(sequence) - 2:
                time_gap = sequence[idx + 2]['timestamp'] - sequence[idx + 1]['timestamp']
                targets['session_end'] = 1 if time_gap > 3600 else 0
        
        return targets
    
    def _get_intent_type_from_action(self, action_type, item_sequence_length):
        """根据行为类型和序列长度推断意图类型"""
        if action_type == 0:  # 浏览
            if item_sequence_length == 1:
                return 1  # 'exploration' 
            else:
                return 2  # 'comparison'
        elif action_type == 1:  # 点击
            return 3  # 'focused'
        else:
            return 1  # 默认为exploration

    def __getitem__(self, uid):
        """增强版数据获取"""
        user_sequence = self._load_user_data(uid)
        
        # 构建层次化序列
        ext_user_sequence, item_sequence = self._build_hierarchical_sequence(user_sequence)
        
        # 基础序列构建（保持原有逻辑）
        seq = np.zeros([self.maxlen + 1], dtype=np.int32)
        pos = np.zeros([self.maxlen + 1], dtype=np.int32)
        neg = np.zeros([self.maxlen + 1], dtype=np.int32)
        token_type = np.zeros([self.maxlen + 1], dtype=np.int32)
        next_token_type = np.zeros([self.maxlen + 1], dtype=np.int32)
        next_action_type = np.zeros([self.maxlen + 1], dtype=np.int32)
        
        # 新增：意图类型和会话信息
        # TODO 这两个信息数据是用来做什么任务的呢
        intent_type = np.zeros([self.maxlen + 1], dtype=np.int32)
        session_position = np.zeros([self.maxlen + 1], dtype=np.int32)
        timestamps = np.zeros([self.maxlen + 1], dtype=np.float32)

        seq_feat = np.empty([self.maxlen + 1], dtype=object)
        pos_feat = np.empty([self.maxlen + 1], dtype=object)
        neg_feat = np.empty([self.maxlen + 1], dtype=object)
        
        nxt = ext_user_sequence[-1]
        idx = self.maxlen
        
        # 用户历史item集合
        ts = set()
        for record_tuple in ext_user_sequence:
            if record_tuple[2] == 1 and record_tuple[0]:
                ts.add(record_tuple[0])
        
        # 构建序列（从后往前）
        current_session_pos = 0
        last_timestamp = float('inf')
        
        for record_tuple in reversed(ext_user_sequence[:-1]):
            i, feat, type_, act_type, timestamp = record_tuple
            next_i, next_feat, next_type, next_act_type, next_timestamp = nxt
            
            # 会话位置更新
            if timestamp < last_timestamp - 3600:  # 新会话
                current_session_pos = 0
            current_session_pos += 1
            
            # 填充基础信息
            feat = self.fill_missing_feat(feat, i)
            next_feat = self.fill_missing_feat(next_feat, next_i)
            
            seq[idx] = i
            token_type[idx] = type_
            next_token_type[idx] = next_type
            session_position[idx] = current_session_pos
            
            if next_act_type is not None:
                next_action_type[idx] = next_act_type
                # intent_type[idx] = next_act_type
            
            intent_type_value = self._get_intent_type_from_action(next_act_type, len(current_session_items))
            intent_type[idx] = intent_type_value
            timestamps[idx] = timestamp  # 添加时间戳

            seq_feat[idx] = feat
            
            # 正样本和负样本
            if next_type == 1 and next_i != 0:
                pos[idx] = next_i
                pos_feat[idx] = next_feat
                
                # 动态负采样
                neg_candidates = self.dynamic_sampler.sample_negatives(
                    user_history=ts,
                    current_item=i,
                    target_item=next_i,
                    user_intent=self._get_current_intent(uid, timestamp),
                    num_samples=1
                )
                
                neg_id = neg_candidates[0] if neg_candidates else self._random_neq(1, self.itemnum + 1, ts)
                neg[idx] = neg_id
                neg_feat[idx] = self.fill_missing_feat(self.item_feat_dict[str(neg_id)], neg_id)
            
            nxt = record_tuple
            last_timestamp = timestamp
            idx -= 1
            if idx == -1:
                break
        
        # 填充默认值
        seq_feat = np.where(seq_feat == None, self.feature_default_value, seq_feat)
        pos_feat = np.where(pos_feat == None, self.feature_default_value, pos_feat)
        neg_feat = np.where(neg_feat == None, self.feature_default_value, neg_feat)
        
        return (seq, pos, neg, token_type, next_token_type, next_action_type,
                intent_type, session_position, timestamps, seq_feat, pos_feat, neg_feat)
    
    def _get_current_intent(self, uid, timestamp):
        """获取当前时刻的用户意图"""
        if uid not in self.user_intent_graph:
            return 'exploration'
        
        # 找到时间戳对应的意图会话
        sessions = self.user_intent_graph[uid]
        for session in reversed(sessions):  # 从最近的会话开始查找
            if session.get('start_time', 0) <= timestamp:
                return session.get('intent_type', 'exploration')
        
        return 'exploration'


class DynamicNegativeSampler:
    """动态负采样器"""
    
    def __init__(self, item_num, item_feat_dict, args):
        self.item_num = item_num
        self.item_feat_dict = item_feat_dict
        
        # 采样策略权重
        self.strategy_weights = {
            'random': 0.3,
            'popular': 0.2,
            'similar': 0.3,
            'hard': 0.2
        }
        
        # 构建item相似度图
        self._build_item_similarity_graph()
    
    def _build_item_similarity_graph(self):
        """构建item相似度图（简化版）"""
        self.item_similarity = defaultdict(list)
        
        # 基于特征相似度构建（这里简化处理）
        # 实际应该使用更复杂的相似度计算
        for item_id in self.item_feat_dict:
            # 随机选择一些相似item（简化）
            similar_items = random.sample(
                list(self.item_feat_dict.keys()), 
                min(10, len(self.item_feat_dict))
            )
            self.item_similarity[item_id] = similar_items
    
    def sample_negatives(self, user_history, current_item, target_item, 
                        user_intent, num_samples=5):
        """动态负采样"""
        candidates = []
        
        for _ in range(num_samples):
            strategy = np.random.choice(
                list(self.strategy_weights.keys()),
                p=list(self.strategy_weights.values())
            )
            
            if strategy == 'random':
                # 随机采样
                neg_item = self._random_sample(user_history)
            elif strategy == 'popular':
                # 流行度采样
                neg_item = self._popularity_sample(user_history)
            elif strategy == 'similar':
                # 相似item采样
                neg_item = self._similarity_sample(target_item, user_history)
            else:  # hard
                # 困难负样本采样
                neg_item = self._hard_sample(current_item, target_item, user_history)
            
            if neg_item:
                candidates.append(neg_item)
        
        return candidates
    
    def _random_sample(self, user_history):
        """随机采样"""
        for _ in range(100):  # 最多尝试100次
            neg_item = np.random.randint(1, self.item_num + 1)
            if neg_item not in user_history and str(neg_item) in self.item_feat_dict:
                return neg_item
        return None
    
    def _popularity_sample(self, user_history):
        """基于流行度的采样"""
        # 简化：随机选择（实际应该基于真实流行度）
        return self._random_sample(user_history)
    
    def _similarity_sample(self, target_item, user_history):
        """基于相似度的采样"""
        if str(target_item) in self.item_similarity:
            similar_items = self.item_similarity[str(target_item)]
            for item in similar_items:
                if int(item) not in user_history:
                    return int(item)
        
        return self._random_sample(user_history)
    
    def _hard_sample(self, current_item, target_item, user_history):
        """困难负样本采样"""
        # 选择与当前item相似但与目标item不同的item
        return self._similarity_sample(current_item, user_history)
