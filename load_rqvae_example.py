#!/usr/bin/env python3
"""
RQ-VAE模型加载示例

展示如何正确加载RQ-VAE模型，解决PyTorch 2.6兼容性问题
"""

import torch
from pathlib import Path
from torch_load_utils import load_rqvae_model, load_rqvae_checkpoint, create_rqvae_model_from_checkpoint
from model_rqvae import RQVAE


def load_rqvae_for_inference(feature_id, rqvae_model_dir, device):
    """
    为推理加载RQ-VAE模型的完整示例
    
    Args:
        feature_id: 特征ID (如 '83', '84', '85', '86')
        rqvae_model_dir: RQ-VAE模型目录
        device: 设备
    
    Returns:
        rqvae_model: 加载好的RQ-VAE模型
    """
    
    # 1. 构建模型路径
    model_path = Path(rqvae_model_dir) / f'rqvae_feat_{feature_id}_best.pt'
    
    if not model_path.exists():
        raise FileNotFoundError(f"RQ-VAE模型文件不存在: {model_path}")
    
    print(f"加载RQ-VAE模型: {model_path}")
    
    # 2. 方法一：手动创建模型然后加载权重（推荐）
    try:
        # 获取特征维度
        SHAPE_DICT = {"81": 32, "82": 1024, "83": 3584, "84": 4096, "85": 3584, "86": 3584}
        input_dim = SHAPE_DICT[feature_id]
        
        # 创建RQ-VAE模型（使用默认参数，实际应该从配置文件加载）
        rqvae_model = RQVAE(
            input_dim=input_dim,
            hidden_channels=[512, 256],
            latent_dim=64,
            num_codebooks=2,
            codebook_size=[256, 256],
            shared_codebook=False,
            kmeans_method='kmeans',
            kmeans_iters=50,
            distances_method='l2',
            loss_beta=0.25,
            device=device
        ).to(device)
        
        # 使用安全加载函数
        rqvae_model, extra_info = load_rqvae_model(rqvae_model, model_path, device)
        
        print(f"✅ 方法一成功: 手动创建模型 + 安全加载")
        if 'epoch' in extra_info:
            print(f"   训练轮数: {extra_info['epoch']}")
        if 'loss' in extra_info:
            print(f"   最佳损失: {extra_info['loss']:.4f}")
        
        return rqvae_model
        
    except Exception as e:
        print(f"❌ 方法一失败: {e}")
    
    # 3. 方法二：从checkpoint自动创建模型（如果checkpoint包含配置）
    try:
        rqvae_model, extra_info = create_rqvae_model_from_checkpoint(
            model_path, device, model_class=RQVAE
        )
        
        print(f"✅ 方法二成功: 从checkpoint自动创建模型")
        return rqvae_model
        
    except Exception as e:
        print(f"❌ 方法二失败: {e}")
    
    # 4. 方法三：直接使用torch.load（兼容性修复）
    try:
        # 直接加载checkpoint
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        
        # 手动创建模型
        SHAPE_DICT = {"81": 32, "82": 1024, "83": 3584, "84": 4096, "85": 3584, "86": 3584}
        input_dim = SHAPE_DICT[feature_id]
        
        rqvae_model = RQVAE(
            input_dim=input_dim,
            hidden_channels=[512, 256],
            latent_dim=64,
            num_codebooks=2,
            codebook_size=[256, 256],
            shared_codebook=False,
            kmeans_method='kmeans',
            kmeans_iters=50,
            distances_method='l2',
            loss_beta=0.25,
            device=device
        ).to(device)
        
        # 加载权重
        if 'model_state_dict' in checkpoint:
            rqvae_model.load_state_dict(checkpoint['model_state_dict'])
        else:
            rqvae_model.load_state_dict(checkpoint)
        
        rqvae_model.eval()
        
        print(f"✅ 方法三成功: 直接torch.load + weights_only=False")
        return rqvae_model
        
    except Exception as e:
        print(f"❌ 方法三失败: {e}")
        raise RuntimeError(f"所有方法都失败了，无法加载RQ-VAE模型: {model_path}")


def convert_embedding_to_semantic_id_safe(embedding, rqvae_model):
    """
    安全地将embedding转换为Semantic ID
    
    Args:
        embedding: 高维embedding向量
        rqvae_model: 训练好的RQ-VAE模型
    
    Returns:
        semantic_id_str: Semantic ID字符串
    """
    try:
        with torch.no_grad():
            if isinstance(embedding, list):
                embedding = torch.tensor(embedding, dtype=torch.float32)
            elif isinstance(embedding, torch.Tensor):
                pass
            else:
                embedding = torch.tensor(embedding, dtype=torch.float32)
            
            # 确保是正确的形状
            if embedding.dim() == 1:
                embedding = embedding.unsqueeze(0)
            
            # 移到正确的设备
            embedding = embedding.to(next(rqvae_model.parameters()).device)
            
            # 获取semantic ID
            semantic_id_list = rqvae_model._get_codebook(embedding)
            
            # 转换为字符串格式
            semantic_id_tensor = semantic_id_list[0]  # 取第一个样本
            semantic_id_str = "_".join([str(id.item()) for id in semantic_id_tensor])
            
            return semantic_id_str
            
    except Exception as e:
        print(f"❌ 转换embedding到semantic ID失败: {e}")
        return None


def test_rqvae_loading():
    """测试RQ-VAE模型加载"""
    
    # 测试参数
    feature_id = '83'  # 可以改为其他特征ID
    rqvae_model_dir = 'rqvae_models'  # RQ-VAE模型目录
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    print(f"测试RQ-VAE模型加载")
    print(f"特征ID: {feature_id}")
    print(f"模型目录: {rqvae_model_dir}")
    print(f"设备: {device}")
    print("=" * 50)
    
    try:
        # 加载模型
        rqvae_model = load_rqvae_for_inference(feature_id, rqvae_model_dir, device)
        
        # 测试转换功能
        print("\n测试embedding转换...")
        
        # 创建测试embedding
        SHAPE_DICT = {"81": 32, "82": 1024, "83": 3584, "84": 4096, "85": 3584, "86": 3584}
        input_dim = SHAPE_DICT[feature_id]
        test_embedding = torch.randn(input_dim)
        
        # 转换为semantic ID
        semantic_id = convert_embedding_to_semantic_id_safe(test_embedding, rqvae_model)
        
        if semantic_id:
            print(f"✅ 测试成功!")
            print(f"   输入维度: {input_dim}")
            print(f"   Semantic ID: {semantic_id}")
        else:
            print(f"❌ 转换失败")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    test_rqvae_loading()
