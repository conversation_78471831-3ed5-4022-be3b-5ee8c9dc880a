"""
增强版RQ-VAE模型

主要改进：
1. 语义感知的量化损失
2. 渐进式码本学习
3. 下游任务适配
4. 多模态特征融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from model_rqvae import RQ, RQVAE, VQEmbedding


class SemanticAwareVQEmbedding(VQEmbedding):
    """语义感知的向量量化"""
    
    def __init__(self, num_clusters, codebook_emb_dim, kmeans_method, 
                 kmeans_iters, distances_method, device, semantic_weight=0.1):
        super().__init__(num_clusters, codebook_emb_dim, kmeans_method, 
                        kmeans_iters, distances_method, device)
        
        self.semantic_weight = semantic_weight
        
        # 语义一致性网络
        self.semantic_net = nn.Sequential(
            nn.Linear(codebook_emb_dim, codebook_emb_dim // 2),
            nn.ReLU(),
            nn.Linear(codebook_emb_dim // 2, codebook_emb_dim // 4),
            nn.ReLU(),
            nn.Linear(codebook_emb_dim // 4, 1)
        ).to(device)
    
    def _compute_semantic_consistency(self, original_emb, quantized_emb):
        """计算语义一致性损失"""
        # 使用语义网络评估量化前后的语义相似性
        original_semantic = self.semantic_net(original_emb)
        quantized_semantic = self.semantic_net(quantized_emb)
        
        semantic_loss = F.mse_loss(original_semantic, quantized_semantic)
        return semantic_loss
    
    def forward(self, data):
        self._create_codebook(data)
        _semantic_id = self._create_semantic_id(data)
        update_emb = self._update_emb(_semantic_id)
        
        # 计算语义一致性损失
        semantic_loss = self._compute_semantic_consistency(data, update_emb)
        
        return update_emb, _semantic_id, semantic_loss


class ProgressiveRQ(RQ):
    """渐进式残差量化"""
    
    def __init__(self, num_codebooks, codebook_size, codebook_emb_dim, 
                 shared_codebook, kmeans_method, kmeans_iters, distances_method, 
                 loss_beta, device, progressive_weight=0.5):
        super().__init__(num_codebooks, codebook_size, codebook_emb_dim, 
                        shared_codebook, kmeans_method, kmeans_iters, 
                        distances_method, loss_beta, device)
        
        self.progressive_weight = progressive_weight
        
        # 渐进式权重（早期码本权重更大）
        self.codebook_weights = nn.Parameter(
            torch.linspace(1.0, 0.5, num_codebooks).to(device)
        )
        
        # 码本利用率监控
        self.register_buffer('codebook_usage', 
                           torch.zeros(num_codebooks, max(codebook_size)))
    
    def _update_codebook_usage(self, semantic_id_list):
        """更新码本使用统计"""
        for i, semantic_ids in enumerate(semantic_id_list.unbind(-1)):
            unique_ids, counts = torch.unique(semantic_ids, return_counts=True)
            self.codebook_usage[i].scatter_add_(0, unique_ids, counts.float())
    
    def _compute_usage_regularization(self):
        """计算码本利用率正则化"""
        usage_reg = 0
        for i in range(self.num_codebooks):
            usage = self.codebook_usage[i]
            # 鼓励均匀使用码本
            usage_entropy = -torch.sum(usage * torch.log(usage + 1e-8))
            usage_reg += -usage_entropy  # 最大化熵
        
        return usage_reg / self.num_codebooks
    
    def quantize(self, data):
        """渐进式量化"""
        res_emb = data.detach().clone()
        
        vq_emb_list, res_emb_list = [], []
        semantic_id_list = []
        vq_emb_aggre = torch.zeros_like(data)
        semantic_losses = []
        
        for i in range(self.num_codebooks):
            if isinstance(self.vqmodules[i], SemanticAwareVQEmbedding):
                vq_emb, _semantic_id, semantic_loss = self.vqmodules[i](res_emb)
                semantic_losses.append(semantic_loss)
            else:
                vq_emb, _semantic_id = self.vqmodules[i](res_emb)
                semantic_losses.append(torch.tensor(0.0, device=data.device))
            
            # 应用渐进式权重
            weighted_vq_emb = vq_emb * self.codebook_weights[i]
            
            res_emb -= weighted_vq_emb
            vq_emb_aggre += weighted_vq_emb
            
            res_emb_list.append(res_emb)
            vq_emb_list.append(vq_emb_aggre)
            semantic_id_list.append(_semantic_id.unsqueeze(dim=-1))
        
        semantic_id_list = torch.cat(semantic_id_list, dim=-1)
        
        # 更新使用统计
        self._update_codebook_usage(semantic_id_list)
        
        return vq_emb_list, res_emb_list, semantic_id_list, semantic_losses
    
    def forward(self, data):
        vq_emb_list, res_emb_list, semantic_id_list, semantic_losses = self.quantize(data)
        
        # 原始RQ-VAE损失
        rqvae_loss = self._rqvae_loss(vq_emb_list, res_emb_list)
        
        # 语义一致性损失
        semantic_loss = sum(semantic_losses) / len(semantic_losses)
        
        # 码本利用率正则化
        usage_reg = self._compute_usage_regularization()
        
        total_loss = rqvae_loss + 0.1 * semantic_loss + 0.01 * usage_reg
        
        return vq_emb_list, semantic_id_list, total_loss


class MultiModalRQVAE(RQVAE):
    """多模态RQ-VAE"""
    
    def __init__(self, input_dims, hidden_channels, latent_dim, num_codebooks, 
                 codebook_size, shared_codebook, kmeans_method, kmeans_iters, 
                 distances_method, loss_beta, device):
        
        # 支持多个输入维度（不同模态）
        self.input_dims = input_dims if isinstance(input_dims, list) else [input_dims]
        self.num_modalities = len(self.input_dims)
        
        # 为每个模态创建编码器
        self.encoders = nn.ModuleList([
            self._create_encoder(dim, hidden_channels, latent_dim).to(device)
            for dim in self.input_dims
        ])
        
        # 共享解码器
        self.decoder = self._create_decoder(latent_dim, hidden_channels, 
                                          sum(self.input_dims)).to(device)
        
        # 模态融合网络
        self.fusion_net = nn.Sequential(
            nn.Linear(latent_dim * self.num_modalities, latent_dim),
            nn.ReLU(),
            nn.Linear(latent_dim, latent_dim)
        ).to(device)
        
        # 增强版RQ
        self.rq = ProgressiveRQ(
            num_codebooks, codebook_size, latent_dim, shared_codebook,
            kmeans_method, kmeans_iters, distances_method, loss_beta, device
        ).to(device)
    
    def _create_encoder(self, input_dim, hidden_channels, latent_dim):
        """创建编码器"""
        layers = []
        in_dim = input_dim
        
        for out_dim in hidden_channels:
            layers.extend([
                nn.Linear(in_dim, out_dim),
                nn.BatchNorm1d(out_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            in_dim = out_dim
        
        layers.append(nn.Linear(in_dim, latent_dim))
        return nn.Sequential(*layers)
    
    def _create_decoder(self, latent_dim, hidden_channels, output_dim):
        """创建解码器"""
        layers = []
        in_dim = latent_dim
        
        for out_dim in reversed(hidden_channels):
            layers.extend([
                nn.Linear(in_dim, out_dim),
                nn.BatchNorm1d(out_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            in_dim = out_dim
        
        layers.append(nn.Linear(in_dim, output_dim))
        return nn.Sequential(*layers)
    
    def encode_multimodal(self, x_list):
        """多模态编码"""
        encoded_list = []
        
        for i, x in enumerate(x_list):
            if i < len(self.encoders):
                encoded = self.encoders[i](x)
                encoded_list.append(encoded)
        
        # 模态融合
        if len(encoded_list) > 1:
            fused = torch.cat(encoded_list, dim=-1)
            fused_encoded = self.fusion_net(fused)
        else:
            fused_encoded = encoded_list[0]
        
        return fused_encoded
    
    def forward(self, x_list):
        """多模态前向传播"""
        # 多模态编码
        z_e = self.encode_multimodal(x_list)
        
        # 量化
        vq_emb_list, semantic_id_list, rqvae_loss = self.rq(z_e)
        
        # 解码
        x_hat = self.decoder(vq_emb_list[-1])
        
        # 重构损失（对所有模态）
        x_concat = torch.cat(x_list, dim=-1)
        recon_loss = F.mse_loss(x_hat, x_concat, reduction="mean")
        
        total_loss = recon_loss + rqvae_loss
        
        return x_hat, semantic_id_list, recon_loss, rqvae_loss, total_loss


class TaskAdaptiveRQVAE(MultiModalRQVAE):
    """任务自适应RQ-VAE"""
    
    def __init__(self, input_dims, hidden_channels, latent_dim, num_codebooks, 
                 codebook_size, shared_codebook, kmeans_method, kmeans_iters, 
                 distances_method, loss_beta, device, num_items=None):
        
        super().__init__(input_dims, hidden_channels, latent_dim, num_codebooks, 
                        codebook_size, shared_codebook, kmeans_method, kmeans_iters, 
                        distances_method, loss_beta, device)
        
        # 下游任务适配头
        if num_items:
            self.task_head = nn.Sequential(
                nn.Linear(latent_dim, latent_dim // 2),
                nn.ReLU(),
                nn.Linear(latent_dim // 2, num_items),
                nn.Softmax(dim=-1)
            ).to(device)
        else:
            self.task_head = None
    
    def compute_task_loss(self, z_vq, item_labels):
        """计算下游任务损失"""
        if self.task_head is None or item_labels is None:
            return torch.tensor(0.0, device=z_vq.device)
        
        # 预测item分布
        item_probs = self.task_head(z_vq)
        
        # 交叉熵损失
        task_loss = F.cross_entropy(item_probs, item_labels)
        
        return task_loss
    
    def forward(self, x_list, item_labels=None):
        """任务自适应前向传播"""
        # 基础前向传播
        x_hat, semantic_id_list, recon_loss, rqvae_loss, total_loss = super().forward(x_list)
        
        # 下游任务损失
        z_vq = self.rq.quantize(self.encode_multimodal(x_list))[0][-1]
        task_loss = self.compute_task_loss(z_vq, item_labels)
        
        # 总损失
        enhanced_total_loss = total_loss + 0.1 * task_loss
        
        return x_hat, semantic_id_list, recon_loss, rqvae_loss, enhanced_total_loss
