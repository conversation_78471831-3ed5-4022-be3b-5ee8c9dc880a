"""
支持Semantic ID的数据集类

修改自原始dataset.py，添加了对RQ-VAE生成的Semantic ID的支持
"""
import os
import json
import pickle
import struct
from pathlib import Path
import numpy as np
import torch
from tqdm import tqdm
from dataset import MyDataset, load_mm_emb, save_emb


class MyDatasetWithSemanticID(MyDataset):
    """
    支持Semantic ID的用户序列数据集
    
    在原有功能基础上，添加了对高维embedding的Semantic ID支持
    """
    
    def __init__(self, data_dir, args):
        """
        初始化数据集，支持Semantic ID
        
        Args:
            data_dir: 数据文件目录
            args: 全局参数，需要包含semantic_id_features参数
        """
        # 调用父类初始化
        super().__init__(data_dir, args)
        
        # 加载Semantic ID映射
        self.semantic_id_features = getattr(args, 'semantic_id_features', [])
        self.semantic_id_mappings = {}
        
        if self.semantic_id_features:
            self._load_semantic_id_mappings()
    
    def _load_semantic_id_mappings(self):
        """加载Semantic ID映射文件"""
        # semantic_id_dir = Path(self.data_dir) / 'semantic_ids'
        semantic_id_dir = Path(self.args.semantic_id_dir)
        
        for feat_id in self.semantic_id_features:
            mapping_file = semantic_id_dir / f'semantic_id_mapping_feat_{feat_id}.json'
            
            if mapping_file.exists():
                with open(mapping_file, 'r') as f:
                    self.semantic_id_mappings[feat_id] = json.load(f)
                print(f'加载Semantic ID映射: {feat_id}, 数量: {len(self.semantic_id_mappings[feat_id])}')
            else:
                print(f'警告: Semantic ID映射文件不存在: {mapping_file}')
                self.semantic_id_mappings[feat_id] = {}
    
    def _init_feat_info(self):
        """
        初始化特征信息，包括Semantic ID特征
        """
        feat_default_value, feat_types, feat_statistics = super()._init_feat_info()
        
        # 添加Semantic ID特征到稀疏特征中
        for feat_id in self.semantic_id_features:
            semantic_feat_name = f'semantic_{feat_id}'
            
            # 添加到item稀疏特征
            feat_types['item_sparse'].append(semantic_feat_name)
            
            # 计算Semantic ID的唯一值数量
            if feat_id in self.semantic_id_mappings:
                unique_semantic_ids = set(self.semantic_id_mappings[feat_id].values())
                feat_statistics[semantic_feat_name] = len(unique_semantic_ids)
                feat_default_value[semantic_feat_name] = 0
                print(f'Semantic ID特征 {semantic_feat_name}: {len(unique_semantic_ids)} 个唯一值')
            else:
                feat_statistics[semantic_feat_name] = 1
                feat_default_value[semantic_feat_name] = 0
        
        return feat_default_value, feat_types, feat_statistics
    
    def fill_missing_feat(self, feat, item_id):
        """
        填充缺失特征，包括Semantic ID特征
        """
        filled_feat = super().fill_missing_feat(feat, item_id)
        
        # 添加Semantic ID特征
        if item_id != 0:
            original_item_id = str(self.indexer_i_rev[item_id])
            
            for feat_id in self.semantic_id_features:
                semantic_feat_name = f'semantic_{feat_id}'
                
                if feat_id in self.semantic_id_mappings:
                    semantic_id_mapping = self.semantic_id_mappings[feat_id]
                    
                    if original_item_id in semantic_id_mapping:
                        # 将Semantic ID字符串转换为数值
                        semantic_id_str = semantic_id_mapping[original_item_id]
                        # 使用hash函数将字符串转换为数值ID
                        semantic_id_num = hash(semantic_id_str) % feat_statistics[semantic_feat_name] + 1
                        filled_feat[semantic_feat_name] = semantic_id_num
                    else:
                        filled_feat[semantic_feat_name] = 0
                else:
                    filled_feat[semantic_feat_name] = 0
        else:
            # item_id为0时，所有Semantic ID特征都设为0
            for feat_id in self.semantic_id_features:
                semantic_feat_name = f'semantic_{feat_id}'
                filled_feat[semantic_feat_name] = 0
        
        return filled_feat


def create_semantic_id_indexer(semantic_id_dir, output_path):
    """
    创建Semantic ID的索引器，用于将字符串ID转换为数值ID
    
    Args:
        semantic_id_dir: Semantic ID映射文件目录
        output_path: 输出索引器文件路径
    """
    semantic_id_dir = Path(semantic_id_dir)
    indexer = {}
    
    # 遍历所有Semantic ID映射文件
    for mapping_file in semantic_id_dir.glob('semantic_id_mapping_feat_*.json'):
        feat_id = mapping_file.stem.split('_')[-1]  # 提取特征ID
        
        with open(mapping_file, 'r') as f:
            semantic_id_mapping = json.load(f)
        
        # 收集所有唯一的Semantic ID
        unique_semantic_ids = set(semantic_id_mapping.values())
        
        # 创建字符串ID到数值ID的映射
        semantic_feat_name = f'semantic_{feat_id}'
        indexer[semantic_feat_name] = {}
        
        for i, semantic_id_str in enumerate(sorted(unique_semantic_ids), 1):
            indexer[semantic_feat_name][semantic_id_str] = i
        
        print(f'特征 {semantic_feat_name}: {len(unique_semantic_ids)} 个唯一Semantic ID')
    
    # 保存索引器
    with open(output_path, 'wb') as f:
        pickle.dump(indexer, f)
    
    print(f'Semantic ID索引器保存到: {output_path}')
    return indexer


class OptimizedMyDatasetWithSemanticID(MyDatasetWithSemanticID):
    """
    优化版本的Semantic ID数据集，使用预计算的索引器
    """
    
    def __init__(self, data_dir, args):
        self.args = args
        self.semantic_id_features = getattr(args, 'semantic_id_features', [])
        self.semantic_id_mappings = {}
        
        if self.semantic_id_features:
            self._load_semantic_id_mappings()
        # 先加载Semantic ID索引器
        self.semantic_id_indexer = {}
        semantic_indexer_path = Path(args.semantic_id_dir) / 'semantic_id_indexer.pkl'
        
        if semantic_indexer_path.exists():
            with open(semantic_indexer_path, 'rb') as f:
                self.semantic_id_indexer = pickle.load(f)
            print(f'加载Semantic ID索引器: {len(self.semantic_id_indexer)} 个特征')
        
        # 调用父类初始化
        super().__init__(data_dir, args)
    
    def fill_missing_feat(self, feat, item_id):
        """
        优化版本的特征填充，使用预计算的索引器
        """
        filled_feat = super(MyDatasetWithSemanticID, self).fill_missing_feat(feat, item_id)
        
        # 添加Semantic ID特征（优化版本）
        if item_id != 0:
            original_item_id = str(self.indexer_i_rev[item_id])
            
            for feat_id in self.semantic_id_features:
                semantic_feat_name = f'semantic_{feat_id}'
                
                if (feat_id in self.semantic_id_mappings and 
                    semantic_feat_name in self.semantic_id_indexer):
                    
                    semantic_id_mapping = self.semantic_id_mappings[feat_id]
                    semantic_indexer = self.semantic_id_indexer[semantic_feat_name]
                    
                    if original_item_id in semantic_id_mapping:
                        semantic_id_str = semantic_id_mapping[original_item_id]
                        if semantic_id_str in semantic_indexer:
                            filled_feat[semantic_feat_name] = semantic_indexer[semantic_id_str]
                        else:
                            filled_feat[semantic_feat_name] = 0
                    else:
                        filled_feat[semantic_feat_name] = 0
                else:
                    filled_feat[semantic_feat_name] = 0
        else:
            # item_id为0时，所有Semantic ID特征都设为0
            for feat_id in self.semantic_id_features:
                semantic_feat_name = f'semantic_{feat_id}'
                filled_feat[semantic_feat_name] = 0
        
        return filled_feat


# 使用示例函数
def prepare_semantic_id_features(data_dir, semantic_id_features, args):
    """
    准备Semantic ID特征的辅助函数
    
    Args:
        data_dir: 数据目录
        semantic_id_features: 要使用的Semantic ID特征列表，如['83', '84']
    """
    # semantic_id_dir = Path(data_dir) / 'semantic_ids'
    semantic_id_dir = Path(args.semantic_id_dir)
    indexer_path = semantic_id_dir / 'semantic_id_indexer.pkl'
    
    if not indexer_path.exists():
        print("创建Semantic ID索引器...")
        os.makedirs(semantic_id_dir, exist_ok=True)
        create_semantic_id_indexer(semantic_id_dir, indexer_path)
    else:
        print(f"Semantic ID索引器已存在: {indexer_path}")
    
    return indexer_path
