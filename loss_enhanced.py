"""
增强版损失函数

主要改进：
1. 多任务对比学习损失
2. 个性化排序损失
3. 时序一致性损失
4. 自适应权重调节
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class MultiTaskContrastiveLoss(nn.Module):
    """多任务对比学习损失"""
    
    def __init__(self, temperature=0.1, margin=0.5):
        super().__init__()
        self.temperature = temperature
        self.margin = margin
        
    def forward(self, user_emb, pos_emb, neg_emb, user_ids=None):
        """
        Args:
            user_emb: 用户表征 [batch_size, hidden_dim]
            pos_emb: 正样本表征 [batch_size, hidden_dim]
            neg_emb: 负样本表征 [batch_size, num_neg, hidden_dim]
            user_ids: 用户ID，用于个性化对比
        """
        batch_size = user_emb.size(0)
        
        # 1. 基础对比损失
        pos_sim = torch.sum(user_emb * pos_emb, dim=-1) / self.temperature
        
        if neg_emb.dim() == 3:  # 多个负样本
            neg_sim = torch.bmm(
                user_emb.unsqueeze(1), 
                neg_emb.transpose(1, 2)
            ).squeeze(1) / self.temperature
        else:  # 单个负样本
            neg_sim = torch.sum(user_emb * neg_emb, dim=-1) / self.temperature
        
        # InfoNCE损失
        if neg_sim.dim() == 1:
            logits = torch.stack([pos_sim, neg_sim], dim=1)
            labels = torch.zeros(batch_size, dtype=torch.long, device=user_emb.device)
            contrastive_loss = F.cross_entropy(logits, labels)
        else:
            # 多负样本情况
            all_sim = torch.cat([pos_sim.unsqueeze(1), neg_sim], dim=1)
            labels = torch.zeros(batch_size, dtype=torch.long, device=user_emb.device)
            contrastive_loss = F.cross_entropy(all_sim, labels)
        
        # 2. 个性化对比损失
        personalized_loss = 0
        if user_ids is not None:
            # 同一用户的不同会话应该相似
            unique_users = torch.unique(user_ids)
            for user_id in unique_users:
                user_mask = (user_ids == user_id)
                if user_mask.sum() > 1:
                    user_embs = user_emb[user_mask]
                    # 用户内部对比
                    user_sim_matrix = torch.mm(user_embs, user_embs.t())
                    user_sim_matrix = user_sim_matrix / self.temperature
                    
                    # 鼓励同用户表征相似
                    user_labels = torch.arange(user_embs.size(0), device=user_emb.device)
                    personalized_loss += F.cross_entropy(user_sim_matrix, user_labels)
        
        return contrastive_loss + 0.1 * personalized_loss


class PersonalizedRankingLoss(nn.Module):
    """个性化排序损失"""
    
    def __init__(self, margin=1.0, reduction='mean'):
        super().__init__()
        self.margin = margin
        self.reduction = reduction
        
    def forward(self, pos_scores, neg_scores, user_preferences=None):
        """
        Args:
            pos_scores: 正样本分数 [batch_size, seq_len]
            neg_scores: 负样本分数 [batch_size, seq_len] 或 [batch_size, seq_len, num_neg]
            user_preferences: 用户偏好权重 [batch_size, seq_len]
        """
        if neg_scores.dim() == 3:
            # 多负样本：选择最高分的负样本
            neg_scores = neg_scores.max(dim=-1)[0]
        
        # BPR损失
        bpr_loss = -torch.log(torch.sigmoid(pos_scores - neg_scores) + 1e-8)
        
        # 个性化权重
        if user_preferences is not None:
            bpr_loss = bpr_loss * user_preferences
        
        # Margin损失
        margin_loss = torch.clamp(self.margin - (pos_scores - neg_scores), min=0)
        
        # 组合损失
        total_loss = bpr_loss + 0.1 * margin_loss
        
        if self.reduction == 'mean':
            return total_loss.mean()
        elif self.reduction == 'sum':
            return total_loss.sum()
        else:
            return total_loss


class TemporalConsistencyLoss(nn.Module):
    """时序一致性损失"""
    
    def __init__(self, alpha=0.5):
        super().__init__()
        self.alpha = alpha
        
    def forward(self, seq_emb, timestamps=None, session_positions=None):
        """
        Args:
            seq_emb: 序列表征 [batch_size, seq_len, hidden_dim]
            timestamps: 时间戳 [batch_size, seq_len]
            session_positions: 会话内位置 [batch_size, seq_len]
        """
        batch_size, seq_len, hidden_dim = seq_emb.shape
        
        # 1. 时序平滑性损失
        temporal_loss = 0
        if seq_len > 1:
            # 相邻时刻的表征应该平滑变化
            diff = seq_emb[:, 1:] - seq_emb[:, :-1]
            temporal_loss = torch.mean(torch.norm(diff, dim=-1))
        
        # 2. 会话一致性损失
        session_loss = 0
        if session_positions is not None:
            # 同一会话内的表征应该相似
            for b in range(batch_size):
                positions = session_positions[b]
                unique_sessions = torch.unique(positions[positions > 0])
                
                for session_id in unique_sessions:
                    session_mask = (positions == session_id)
                    if session_mask.sum() > 1:
                        session_embs = seq_emb[b][session_mask]
                        # 计算会话内方差
                        session_mean = session_embs.mean(dim=0)
                        session_var = torch.mean(torch.norm(session_embs - session_mean, dim=-1))
                        session_loss += session_var
        
        return self.alpha * temporal_loss + (1 - self.alpha) * session_loss


class AdaptiveWeightedLoss(nn.Module):
    """自适应权重损失"""
    
    def __init__(self, num_tasks=4, init_weights=None):
        super().__init__()
        self.num_tasks = num_tasks
        
        if init_weights is None:
            init_weights = torch.ones(num_tasks) / num_tasks
        
        # 可学习的权重参数
        self.log_weights = nn.Parameter(torch.log(init_weights))
        
        # 损失历史（用于自适应调整）
        self.register_buffer('loss_history', torch.zeros(num_tasks, 100))
        self.register_buffer('history_idx', torch.tensor(0))
        
    def forward(self, losses, update_weights=True):
        """
        Args:
            losses: 各任务损失列表 [loss1, loss2, ...]
            update_weights: 是否更新权重
        """
        losses = torch.stack(losses)
        weights = F.softmax(self.log_weights, dim=0)
        
        # 更新损失历史
        if update_weights and self.training:
            idx = self.history_idx % 100
            self.loss_history[:, idx] = losses.detach()
            self.history_idx += 1
            
            # 基于损失变化调整权重
            if self.history_idx > 10:
                recent_losses = self.loss_history[:, max(0, idx-10):idx+1]
                loss_trends = recent_losses.std(dim=1)
                
                # 损失变化大的任务给更高权重
                adaptive_weights = F.softmax(loss_trends / 0.1, dim=0)
                weights = 0.9 * weights + 0.1 * adaptive_weights
        
        # 加权损失
        weighted_loss = torch.sum(weights * losses)
        
        return weighted_loss, weights


class EnhancedLossFunction(nn.Module):
    """增强版损失函数"""
    
    def __init__(self, args):
        super().__init__()
        
        # 各种损失组件
        self.bce_loss = nn.BCEWithLogitsLoss(reduction='mean')
        self.contrastive_loss = MultiTaskContrastiveLoss(
            temperature=getattr(args, 'temperature', 0.1)
        )
        self.ranking_loss = PersonalizedRankingLoss(
            margin=getattr(args, 'margin', 1.0)
        )
        self.temporal_loss = TemporalConsistencyLoss(
            alpha=getattr(args, 'temporal_alpha', 0.5)
        )
        self.adaptive_weights = AdaptiveWeightedLoss(num_tasks=4)
        
        # 损失权重
        self.loss_weights = {
            'bce': getattr(args, 'bce_weight', 1.0),
            'contrastive': getattr(args, 'contrastive_weight', 0.5),
            'ranking': getattr(args, 'ranking_weight', 0.3),
            'temporal': getattr(args, 'temporal_weight', 0.2)
        }
    
    def forward(self, pos_logits, neg_logits, user_emb, pos_emb, neg_emb,
                seq_emb, next_mask, user_ids=None, timestamps=None, 
                session_positions=None, user_preferences=None):
        """
        计算综合损失
        
        Args:
            pos_logits: 正样本logits
            neg_logits: 负样本logits
            user_emb: 用户表征
            pos_emb: 正样本表征
            neg_emb: 负样本表征
            seq_emb: 序列表征
            next_mask: 下一个token掩码
            user_ids: 用户ID
            timestamps: 时间戳
            session_positions: 会话位置
            user_preferences: 用户偏好权重
        """
        losses = []
        loss_info = {}
        
        # 1. BCE损失（基础损失）
        indices = (next_mask == 1)
        if indices.any():
            pos_labels = torch.ones_like(pos_logits[indices])
            neg_labels = torch.zeros_like(neg_logits[indices])
            
            bce_pos = self.bce_loss(pos_logits[indices], pos_labels)
            bce_neg = self.bce_loss(neg_logits[indices], neg_labels)
            bce_loss = bce_pos + bce_neg
        else:
            bce_loss = torch.tensor(0.0, device=pos_logits.device)
        
        losses.append(bce_loss)
        loss_info['bce'] = bce_loss.item()
        
        # 2. 对比学习损失
        if user_emb is not None and pos_emb is not None and neg_emb is not None:
            # 只使用有效位置的表征
            if indices.any():
                valid_user_emb = user_emb[indices]
                valid_pos_emb = pos_emb[indices]
                valid_neg_emb = neg_emb[indices]
                valid_user_ids = user_ids[indices] if user_ids is not None else None
                
                contrastive_loss = self.contrastive_loss(
                    valid_user_emb, valid_pos_emb, valid_neg_emb, valid_user_ids
                )
            else:
                contrastive_loss = torch.tensor(0.0, device=pos_logits.device)
        else:
            contrastive_loss = torch.tensor(0.0, device=pos_logits.device)
        
        losses.append(contrastive_loss)
        loss_info['contrastive'] = contrastive_loss.item()
        
        # 3. 排序损失
        if indices.any():
            valid_pos_scores = pos_logits[indices]
            valid_neg_scores = neg_logits[indices]
            valid_preferences = user_preferences[indices] if user_preferences is not None else None
            
            ranking_loss = self.ranking_loss(
                valid_pos_scores, valid_neg_scores, valid_preferences
            )
        else:
            ranking_loss = torch.tensor(0.0, device=pos_logits.device)
        
        losses.append(ranking_loss)
        loss_info['ranking'] = ranking_loss.item()
        
        # 4. 时序一致性损失
        if seq_emb is not None:
            temporal_loss = self.temporal_loss(seq_emb, timestamps, session_positions)
        else:
            temporal_loss = torch.tensor(0.0, device=pos_logits.device)
        
        losses.append(temporal_loss)
        loss_info['temporal'] = temporal_loss.item()
        
        # 5. 自适应权重组合
        total_loss, current_weights = self.adaptive_weights(losses)
        
        loss_info['total'] = total_loss.item()
        loss_info['weights'] = current_weights.detach().cpu().numpy().tolist()
        
        return total_loss, loss_info
