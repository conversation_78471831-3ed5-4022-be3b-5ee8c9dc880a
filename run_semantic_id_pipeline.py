#!/usr/bin/env python3
"""
RQ-VAE Semantic ID完整流程脚本

这个脚本实现了完整的RQ-VAE使用流程：
1. 训练RQ-VAE模型压缩高维embedding
2. 生成Semantic ID映射
3. 使用Semantic ID训练推荐模型

使用方法：
python run_semantic_id_pipeline.py --features 83 84 --epochs 20
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path
import json


def get_args():
    parser = argparse.ArgumentParser(description='RQ-VAE Semantic ID完整流程')
    
    # 要处理的特征ID
    parser.add_argument('--features', nargs='+', default=['83', '84'], 
                       choices=['82', '83', '84', '85', '86'],
                       help='要压缩的高维embedding特征ID')
    
    # RQ-VAE训练参数
    parser.add_argument('--rqvae_epochs', default=20, type=int, help='RQ-VAE训练轮数')
    parser.add_argument('--rqvae_batch_size', default=512, type=int, help='RQ-VAE批次大小')
    parser.add_argument('--rqvae_lr', default=1e-3, type=float, help='RQ-VAE学习率')
    
    # 主模型训练参数
    parser.add_argument('--main_epochs', default=3, type=int, help='主模型训练轮数')
    parser.add_argument('--main_batch_size', default=128, type=int, help='主模型批次大小')
    parser.add_argument('--main_lr', default=0.001, type=float, help='主模型学习率')
    
    # 其他参数
    parser.add_argument('--device', default='cuda', type=str, help='设备')
    parser.add_argument('--data_dir', default='data/TencentGR_1k', type=str, help='数据目录')
    parser.add_argument('--skip_rqvae', action='store_true', help='跳过RQ-VAE训练（如果已经训练过）')
    parser.add_argument('--only_rqvae', action='store_true', help='只训练RQ-VAE，不训练主模型')
    
    return parser.parse_args()


def check_gpu_memory():
    """检查GPU内存使用情况"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"GPU总内存: {gpu_memory:.1f} GB")
            
            # 检查当前内存使用
            torch.cuda.empty_cache()
            allocated = torch.cuda.memory_allocated(0) / 1024**3
            cached = torch.cuda.memory_reserved(0) / 1024**3
            print(f"已分配内存: {allocated:.1f} GB")
            print(f"缓存内存: {cached:.1f} GB")
            print(f"可用内存: {gpu_memory - cached:.1f} GB")
            
            return gpu_memory - cached > 2.0  # 至少需要2GB可用内存
        else:
            print("CUDA不可用")
            return False
    except ImportError:
        print("PyTorch未安装")
        return False


def train_rqvae_for_feature(feature_id, args):
    """为指定特征训练RQ-VAE模型"""
    print(f"\n{'='*60}")
    print(f"训练RQ-VAE模型 - 特征ID: {feature_id}")
    print(f"{'='*60}")
    
    # 检查模型是否已存在
    model_path = Path('rqvae_models') / f'rqvae_feat_{feature_id}_best.pt'
    if model_path.exists() and args.skip_rqvae:
        print(f"RQ-VAE模型已存在，跳过训练: {model_path}")
        return True
    
    # 构建训练命令
    cmd = [
        sys.executable, 'train_rqvae_compress.py',
        '--feature_id', feature_id,
        '--num_epochs', str(args.rqvae_epochs),
        '--batch_size', str(args.rqvae_batch_size),
        '--lr', str(args.rqvae_lr),
        '--device', args.device,
        '--data_dir', args.data_dir
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("RQ-VAE训练成功完成")
        print("输出:", result.stdout[-500:])  # 显示最后500个字符
        return True
    except subprocess.CalledProcessError as e:
        print(f"RQ-VAE训练失败: {e}")
        print("错误输出:", e.stderr)
        return False


def train_main_model_with_semantic_id(args):
    """使用Semantic ID训练主模型"""
    print(f"\n{'='*60}")
    print("训练主模型 - 使用Semantic ID特征")
    print(f"{'='*60}")
    
    # 构建训练命令
    cmd = [
        sys.executable, 'main_with_semantic_id.py',
        '--num_epochs', str(args.main_epochs),
        '--batch_size', str(args.main_batch_size),
        '--lr', str(args.main_lr),
        '--device', args.device,
        '--semantic_id_features'] + args.features + [
        '--mm_emb_id', '81',  # 只使用低维的embedding
        '--experiment_name', f'semantic_id_{"_".join(args.features)}'
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("主模型训练成功完成")
        print("输出:", result.stdout[-500:])  # 显示最后500个字符
        return True
    except subprocess.CalledProcessError as e:
        print(f"主模型训练失败: {e}")
        print("错误输出:", e.stderr)
        return False


def check_semantic_id_files(features):
    """检查Semantic ID文件是否存在"""
    semantic_id_dir = Path('semantic_ids')
    missing_files = []
    
    for feature_id in features:
        mapping_file = semantic_id_dir / f'semantic_id_mapping_feat_{feature_id}.json'
        if not mapping_file.exists():
            missing_files.append(str(mapping_file))
    
    if missing_files:
        print("缺少以下Semantic ID映射文件:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    
    return True


def print_summary(args, success_features, failed_features):
    """打印执行总结"""
    print(f"\n{'='*60}")
    print("执行总结")
    print(f"{'='*60}")
    
    print(f"目标特征: {args.features}")
    print(f"成功处理的特征: {success_features}")
    if failed_features:
        print(f"失败的特征: {failed_features}")
    
    print(f"\nRQ-VAE训练参数:")
    print(f"  - 训练轮数: {args.rqvae_epochs}")
    print(f"  - 批次大小: {args.rqvae_batch_size}")
    print(f"  - 学习率: {args.rqvae_lr}")
    
    if not args.only_rqvae:
        print(f"\n主模型训练参数:")
        print(f"  - 训练轮数: {args.main_epochs}")
        print(f"  - 批次大小: {args.main_batch_size}")
        print(f"  - 学习率: {args.main_lr}")
    
    print(f"\n输出目录:")
    print(f"  - RQ-VAE模型: ./rqvae_models/")
    print(f"  - Semantic ID映射: ./semantic_ids/")
    if not args.only_rqvae:
        print(f"  - 主模型: ./checkpoints/semantic_id_{'_'.join(args.features)}/")


def main():
    args = get_args()
    
    print("RQ-VAE Semantic ID完整流程")
    print(f"要处理的特征: {args.features}")
    print(f"设备: {args.device}")
    
    # 检查GPU内存
    if args.device == 'cuda':
        if not check_gpu_memory():
            print("警告: GPU内存可能不足，建议使用CPU或减少批次大小")
    
    success_features = []
    failed_features = []
    
    # 第一步：为每个特征训练RQ-VAE模型
    if not args.skip_rqvae:
        for feature_id in args.features:
            if train_rqvae_for_feature(feature_id, args):
                success_features.append(feature_id)
            else:
                failed_features.append(feature_id)
                print(f"特征 {feature_id} 的RQ-VAE训练失败，跳过")
    else:
        print("跳过RQ-VAE训练")
        success_features = args.features
    
    # 检查Semantic ID文件是否生成
    if success_features and not check_semantic_id_files(success_features):
        print("错误: 缺少必要的Semantic ID映射文件")
        return
    
    # 第二步：使用Semantic ID训练主模型
    if not args.only_rqvae and success_features:
        print(f"\n使用成功处理的特征训练主模型: {success_features}")
        # 更新args中的features为成功处理的特征
        args.features = success_features
        
        if train_main_model_with_semantic_id(args):
            print("主模型训练成功完成")
        else:
            print("主模型训练失败")
    elif args.only_rqvae:
        print("只训练RQ-VAE，跳过主模型训练")
    
    # 打印总结
    print_summary(args, success_features, failed_features)


if __name__ == '__main__':
    main()
