#!/usr/bin/env python3
"""
运行支持Semantic ID的推理脚本

使用方法:
python run_inference_with_semantic_id.py --semantic_id_features 83 84 85 86 --mm_emb_id 81
"""

import os
import sys
import argparse
from pathlib import Path

def setup_environment():
    """设置环境变量"""
    # 设置数据路径（根据实际情况修改）
    if not os.environ.get('EVAL_DATA_PATH'):
        os.environ['EVAL_DATA_PATH'] = './data/TencentGR_1k'
    
    if not os.environ.get('EVAL_RESULT_PATH'):
        os.environ['EVAL_RESULT_PATH'] = './inference_results'
    
    if not os.environ.get('MODEL_OUTPUT_PATH'):
        os.environ['MODEL_OUTPUT_PATH'] = './checkpoints'
    
    # 创建结果目录
    Path(os.environ['EVAL_RESULT_PATH']).mkdir(exist_ok=True)
    
    print("环境变量设置:")
    print(f"  EVAL_DATA_PATH: {os.environ['EVAL_DATA_PATH']}")
    print(f"  EVAL_RESULT_PATH: {os.environ['EVAL_RESULT_PATH']}")
    print(f"  MODEL_OUTPUT_PATH: {os.environ['MODEL_OUTPUT_PATH']}")


def main():
    parser = argparse.ArgumentParser(description='运行支持Semantic ID的推理')
    
    # 基本参数
    parser.add_argument('--batch_size', default=128, type=int, help='批次大小')
    parser.add_argument('--device', default='cuda', type=str, help='设备')
    parser.add_argument('--maxlen', default=101, type=int, help='序列最大长度')
    
    # 模型参数
    parser.add_argument('--hidden_units', default=32, type=int)
    parser.add_argument('--num_blocks', default=1, type=int)
    parser.add_argument('--num_heads', default=1, type=int)
    parser.add_argument('--dropout_rate', default=0.2, type=float)
    
    # 特征参数
    parser.add_argument('--mm_emb_id', nargs='+', default=['81'], type=str,
                       help='原始多模态特征ID (建议只用低维度的81)')
    parser.add_argument('--semantic_id_features', nargs='+', default=['83', '84'], type=str,
                       help='要转换为Semantic ID的特征ID')
    
    # RQ-VAE模型路径
    parser.add_argument('--rqvae_model_dir', default='rqvae_models', type=str,
                       help='RQ-VAE模型目录')
    
    # 输出选项
    parser.add_argument('--save_results', action='store_true',
                       help='是否保存推理结果到文件')
    parser.add_argument('--output_file', default='inference_results.json', type=str,
                       help='结果输出文件名')
    
    args = parser.parse_args()
    
    # 设置环境
    setup_environment()
    
    # 检查RQ-VAE模型是否存在
    rqvae_dir = Path(args.rqvae_model_dir)
    if not rqvae_dir.exists():
        print(f"错误: RQ-VAE模型目录不存在: {rqvae_dir}")
        print("请先运行 train_rqvae_compress.py 训练RQ-VAE模型")
        sys.exit(1)
    
    missing_models = []
    for feat_id in args.semantic_id_features:
        model_path = rqvae_dir / f'rqvae_feat_{feat_id}_best.pt'
        if not model_path.exists():
            missing_models.append(feat_id)
    
    if missing_models:
        print(f"警告: 以下特征的RQ-VAE模型不存在: {missing_models}")
        print("将从semantic_id_features中移除这些特征")
        args.semantic_id_features = [f for f in args.semantic_id_features if f not in missing_models]
    
    if not args.semantic_id_features:
        print("错误: 没有可用的Semantic ID特征，请先训练RQ-VAE模型")
        sys.exit(1)
    
    print(f"将使用以下Semantic ID特征: {args.semantic_id_features}")
    print(f"将使用以下原始多模态特征: {args.mm_emb_id}")
    
    # 导入并运行推理
    try:
        # 动态设置命令行参数
        sys.argv = ['infer_with_semantic_id.py'] + [
            '--batch_size', str(args.batch_size),
            '--device', args.device,
            '--maxlen', str(args.maxlen),
            '--hidden_units', str(args.hidden_units),
            '--num_blocks', str(args.num_blocks),
            '--num_heads', str(args.num_heads),
            '--dropout_rate', str(args.dropout_rate),
            '--mm_emb_id'] + args.mm_emb_id + [
            '--semantic_id_features'] + args.semantic_id_features + [
            '--rqvae_model_dir', args.rqvae_model_dir
        ]
        
        from infer_with_semantic_id import infer_with_semantic_id
        
        print("开始推理...")
        top10s, user_list = infer_with_semantic_id()
        
        print(f"推理完成！")
        print(f"处理用户数: {len(user_list)}")
        print(f"每用户推荐数: {len(top10s[0]) if top10s else 0}")
        
        # 保存结果
        if args.save_results:
            import json
            results = {
                'users': user_list,
                'recommendations': top10s,
                'config': {
                    'semantic_id_features': args.semantic_id_features,
                    'mm_emb_id': args.mm_emb_id,
                    'batch_size': args.batch_size,
                    'device': args.device
                }
            }
            
            output_path = Path(os.environ['EVAL_RESULT_PATH']) / args.output_file
            with open(output_path, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"结果已保存到: {output_path}")
        
        # 显示部分结果示例
        if top10s and user_list:
            print("\n=== 推理结果示例 ===")
            for i in range(min(3, len(user_list))):
                print(f"用户 {user_list[i]}: {top10s[i]}")
        
        return top10s, user_list
        
    except Exception as e:
        print(f"推理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
