import os
import psutil
import torch
from pathlib import Path
import time

def log_system_status(log_file):
    # 获取内存信息
    mem = psutil.virtual_memory()
    gpu_mem = []
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            alloc = torch.cuda.memory_allocated(i) / (1024 ** 3)  # GB
            reserved = torch.cuda.memory_reserved(i) / (1024 ** 3)  # GB
            gpu_mem.append(f"GPU{i}: alloc={alloc:.2f}GB, reserved={reserved:.2f}GB")
    
    log_file.write(f"\n[Status] Time: {time.ctime()}\n")
    log_file.write(f"System memory: used={mem.used/(1024**3):.2f}GB, "
                  f"available={mem.available/(1024**3):.2f}GB, "
                  f"percent={mem.percent}%\n")
    if gpu_mem:
        log_file.write("GPU memory: " + ", ".join(gpu_mem) + "\n")
    log_file.flush()

def log_hardware_info(log_file):
    import tensorflow as tf
    log_file.write("\n===== Hardware Info =====\n")
    log_file.write(f"TensorFlow version: {tf.__version__}\n")
    log_file.write(f"Visible GPUs: {tf.config.list_physical_devices('GPU')}\n")
    log_file.write(f"CPUs: {psutil.cpu_count()}\n")
    log_file.write(f"System memory: {psutil.virtual_memory().total/(1024**3):.2f}GB\n")
    if torch.cuda.is_available():
        log_file.write(f"PyTorch CUDA version: {torch.version.cuda}\n")
        for i in range(torch.cuda.device_count()):
            log_file.write(f"GPU {i}: {torch.cuda.get_device_name(i)}\n")
            log_file.write(f"  Memory: {torch.cuda.get_device_properties(i).total_memory/(1024**3):.2f}GB\n")
    log_file.flush()

    