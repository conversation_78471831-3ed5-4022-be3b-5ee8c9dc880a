"""
支持Semantic ID的推理代码

主要功能：
1. 加载训练好的RQ-VAE模型，将高维embedding转换为Semantic ID
2. 加载训练好的主模型进行预测
3. 支持测试集的Semantic ID特征处理
"""

import argparse
import json
import os
import struct
from pathlib import Path
import pickle

import numpy as np
import torch
from torch.utils.data import DataLoader
from tqdm import tqdm

from dataset import MyTestDataset, save_emb
from dataset_with_semantic_id import MyTestDatasetWithSemanticID
from model import BaselineModel
from model_rqvae import RQVAE, MmEmbDataset


def get_ckpt_path():
    """获取模型检查点路径"""
    ckpt_path = os.environ.get("MODEL_OUTPUT_PATH")
    if ckpt_path is None:
        raise ValueError("MODEL_OUTPUT_PATH is not set")
    for item in os.listdir(ckpt_path):
        if item.endswith(".pt"):
            return os.path.join(ckpt_path, item)


def get_args():
    parser = argparse.ArgumentParser()

    # Train params
    parser.add_argument('--batch_size', default=128, type=int)
    parser.add_argument('--lr', default=0.001, type=float)
    parser.add_argument('--maxlen', default=101, type=int)

    # Baseline Model construction
    parser.add_argument('--hidden_units', default=32, type=int)
    parser.add_argument('--num_blocks', default=1, type=int)
    parser.add_argument('--num_epochs', default=3, type=int)
    parser.add_argument('--num_heads', default=1, type=int)
    parser.add_argument('--dropout_rate', default=0.2, type=float)
    parser.add_argument('--l2_emb', default=0.0, type=float)
    parser.add_argument('--device', default='cuda', type=str)
    parser.add_argument('--inference_only', action='store_true')
    parser.add_argument('--state_dict_path', default=None, type=str)
    parser.add_argument('--norm_first', action='store_true')

    # MMemb Feature ID
    parser.add_argument('--mm_emb_id', nargs='+', default=['81'], type=str, 
                       choices=[str(s) for s in range(81, 87)])
    
    # Semantic ID features
    parser.add_argument('--semantic_id_features', nargs='+', default=['82'], type=str)
    
    # RQ-VAE model paths
    parser.add_argument('--rqvae_model_dir', default='rqvae_models', type=str,
                       help='Directory containing trained RQ-VAE models')
    parser.add_argument('--num_codebooks', default=2, type=int)
    parser.add_argument('--codebook_size', default=256, type=int)
    parser.add_argument('--hidden_channels', default=[512, 256], type=list, help='隐藏层维度')
    args = parser.parse_args()
    return args


def load_rqvae_models(rqvae_model_dir, semantic_id_features, device):
    """加载训练好的RQ-VAE模型"""
    rqvae_models = {}
    
    for feat_id in semantic_id_features:
        model_path = Path(rqvae_model_dir) / f'rqvae_feat_{feat_id}_best.pt'
        
        if not model_path.exists():
            print(f"警告: RQ-VAE模型文件不存在: {model_path}")
            continue
            
        # 加载checkpoint
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        
        # 创建模型
        SHAPE_DICT = {"81": 32, "82": 1024, "83": 3584, "84": 4096, "85": 3584, "86": 3584}
        input_dim = SHAPE_DICT[feat_id]
        
        rqvae_model = RQVAE(
            input_dim=input_dim,
            hidden_channels=[512, 256],
            latent_dim=64,
            num_codebooks=2,
            codebook_size=[256, 256],
            shared_codebook=False,
            kmeans_method='kmeans',
            kmeans_iters=100,
            distances_method='l2',
            loss_beta=0.25,
            device=device
        ).to(device)
        
        # 关键：加载state_dict时要包含codebook
        model_dict = rqvae_model.state_dict()
        pretrained_dict = {}
        
        for k, v in checkpoint['model_state_dict'].items():
            if k in model_dict and model_dict[k].shape == v.shape:
                pretrained_dict[k] = v
                print(f"✅ 加载参数: {k}")
            elif k.endswith('.codebook'):
                # 特殊处理codebook参数
                pretrained_dict[k] = v
                print(f"✅ 加载codebook: {k}, 参数dim：{v.shape}")
            else:
                print(f"⚠️ 跳过参数: {k}")
        
        model_dict.update(pretrained_dict)
        rqvae_model.load_state_dict(model_dict, strict=False)
        
        # 验证codebook是否正确加载
        for i, vq_module in enumerate(rqvae_model.rq.vqmodules):
            if hasattr(vq_module, 'codebook') and vq_module.codebook is not None:
                print(f"✅ VQ模块 {i} codebook已加载: {vq_module.codebook.shape}")
            else:
                print(f"❌ VQ模块 {i} codebook未加载，需要手动初始化")
                # 手动初始化codebook
                dummy_data = torch.randn(1000, vq_module.codebook_emb_dim).to(device)
                vq_module._create_codebook(dummy_data)
                print(f"✅ 手动初始化codebook完成: {vq_module.codebook.shape}, dummy_data shape: {dummy_data.shape}")
        
        rqvae_model.eval()
        rqvae_models[feat_id] = rqvae_model
        print(f"成功加载RQ-VAE模型: {feat_id}")
    
    return rqvae_models

def load_rqvae_models_two(rqvae_model_dir, semantic_id_features, device):
    """加载训练好的RQ-VAE模型"""
    rqvae_models = {}
    
    for feat_id in semantic_id_features:
        model_path = Path(rqvae_model_dir) / f'rqvae_feat_{feat_id}_best.pt'
        
        if not model_path.exists():
            print(f"警告: RQ-VAE模型文件不存在: {model_path}")
            continue
            
        # 加载checkpoint
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        
        # 获取保存的参数
        saved_args = checkpoint.get('args', None)
        
        # 使用保存的参数创建模型（如果有的话）
        
        # 使用默认参数
        SHAPE_DICT = {"81": 32, "82": 1024, "83": 3584, "84": 4096, "85": 3584, "86": 3584}
        rqvae_model = RQVAE(
                input_dim=SHAPE_DICT[feat_id],
                hidden_channels=[512, 256],
                latent_dim=64,
                num_codebooks=2,
                codebook_size=[256, 256],
                shared_codebook=False,
                kmeans_method='kmeans',
                kmeans_iters=100,
                distances_method='l2',
                loss_beta=0.25,
                device=device
        )
        
        # 先初始化codebook（关键步骤）
        # 创建一些dummy数据来触发codebook初始化
        dummy_input = torch.randn(100, rqvae_model.rq.codebook_emb_dim).to(device)
        with torch.no_grad():
            _ = rqvae_model.rq(dummy_input)  # 这会初始化codebook
        
        # 现在加载state_dict
        try:
            rqvae_model.load_state_dict(checkpoint['model_state_dict'])
        except RuntimeError as e:
            print(f"直接加载失败，尝试部分加载: {e}")
            # 部分加载，忽略不匹配的键
            model_dict = rqvae_model.state_dict()
            pretrained_dict = {k: v for k, v in checkpoint['model_state_dict'].items() 
                             if k in model_dict and model_dict[k].shape == v.shape}
            model_dict.update(pretrained_dict)
            rqvae_model.load_state_dict(model_dict)
            print(f"部分加载成功，加载了 {len(pretrained_dict)} 个参数")
        
        rqvae_model.eval()
        rqvae_models[feat_id] = rqvae_model
        print(f"成功加载RQ-VAE模型: {feat_id}")
    
    return rqvae_models

def convert_embedding_to_semantic_id(embedding, rqvae_model):
    """
    使用RQ-VAE模型将embedding转换为Semantic ID
    
    Args:
        embedding: 高维embedding向量
        rqvae_model: 训练好的RQ-VAE模型
    
    Returns:
        semantic_id_str: Semantic ID字符串
    """
    with torch.no_grad():
        if isinstance(embedding, np.ndarray):
            embedding = torch.tensor(embedding, dtype=torch.float32)
        
        embedding = embedding.unsqueeze(0).to(rqvae_model.encoder.stages[0][0].weight.device)
        semantic_id_list = rqvae_model._get_codebook(embedding)
        
        # 转换为字符串格式
        semantic_id_tensor = semantic_id_list[0]  # 取第一个样本
        semantic_id_str = "_".join([str(id.item()) for id in semantic_id_tensor])
        
    return semantic_id_str


def generate_semantic_ids_for_candidates(candidate_path, rqvae_models, semantic_id_features, mm_emb_dict):
    """
    为候选集生成Semantic ID映射
    
    Args:
        candidate_path: 候选集文件路径
        rqvae_models: RQ-VAE模型字典
        semantic_id_features: Semantic ID特征列表
        mm_emb_dict: 多模态embedding字典
    
    Returns:
        semantic_id_mappings: Semantic ID映射字典
    """
    semantic_id_mappings = {feat_id: {} for feat_id in semantic_id_features}
    
    print("正在为候选集生成Semantic ID...")
    
    with open(candidate_path, 'r') as f:
        for line in tqdm(f, desc="处理候选集"):
            line_data = json.loads(line)
            creative_id = line_data['creative_id']
            
            for feat_id in semantic_id_features:
                if feat_id in rqvae_models and feat_id in mm_emb_dict:
                    if creative_id in mm_emb_dict[feat_id]:
                        embedding = mm_emb_dict[feat_id][creative_id]
                        semantic_id_str = convert_embedding_to_semantic_id(embedding, rqvae_models[feat_id])
                        semantic_id_mappings[feat_id][creative_id] = semantic_id_str
    
    return semantic_id_mappings


def save_semantic_id_mappings(semantic_id_mappings, output_dir):
    """保存Semantic ID映射到文件"""
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    for feat_id, mapping in semantic_id_mappings.items():
        output_file = output_dir / f'semantic_id_mapping_feat_{feat_id}.json'
        with open(output_file, 'w') as f:
            json.dump(mapping, f)
        print(f"保存Semantic ID映射: {output_file}")


def read_result_ids(file_path):
    """读取ANN检索结果"""
    with open(file_path, 'rb') as f:
        # Read the header (num_points_query and FLAGS_query_ann_top_k)
        num_points_query = struct.unpack('I', f.read(4))[0]  # uint32_t -> 4 bytes
        query_ann_top_k = struct.unpack('I', f.read(4))[0]  # uint32_t -> 4 bytes

        print(f"num_points_query: {num_points_query}, query_ann_top_k: {query_ann_top_k}")

        # Calculate how many result_ids there are (num_points_query * query_ann_top_k)
        num_result_ids = num_points_query * query_ann_top_k

        # Read result_ids (uint64_t, 8 bytes per value)
        result_ids = np.fromfile(f, dtype=np.uint64, count=num_result_ids)

        return result_ids.reshape((num_points_query, query_ann_top_k))


def process_cold_start_feat(feat):
    """
    处理冷启动特征。训练集未出现过的特征value为字符串，默认转换为0.可设计替换为更好的方法。
    """
    processed_feat = {}
    for feat_id, feat_value in feat.items():
        if type(feat_value) == list:
            value_list = []
            for v in feat_value:
                if type(v) == str:
                    value_list.append(0)
                else:
                    value_list.append(v)
            processed_feat[feat_id] = value_list
        elif type(feat_value) == str:
            processed_feat[feat_id] = 0
        else:
            processed_feat[feat_id] = feat_value
    return processed_feat


def get_candidate_emb_with_semantic_id(indexer, feat_types, feat_default_value, mm_emb_dict,
                                     model, semantic_id_mappings, semantic_id_indexer):
    """
    生成候选库item的id和embedding（支持Semantic ID）

    Args:
        indexer: 索引字典
        feat_types: 特征类型
        feat_default_value: 特征缺省值
        mm_emb_dict: 多模态特征字典
        model: 模型
        semantic_id_mappings: Semantic ID映射
        semantic_id_indexer: Semantic ID索引器

    Returns:
        retrieve_id2creative_id: 索引id->creative_id的dict
    """
    EMB_SHAPE_DICT = {"81": 32, "82": 1024, "83": 3584, "84": 4096, "85": 3584, "86": 3584}
    candidate_path = Path(os.environ.get('EVAL_DATA_PATH'), 'predict_set.jsonl')
    item_ids, creative_ids, retrieval_ids, features = [], [], [], []
    retrieve_id2creative_id = {}

    with open(candidate_path, 'r') as f:
        for line in f:
            line = json.loads(line)
            # 读取item特征，并补充缺失值
            feature = line['features']
            creative_id = line['creative_id']
            retrieval_id = line['retrieval_id']
            item_id = indexer[creative_id] if creative_id in indexer else 0

            # 处理传统特征
            missing_fields = set(
                feat_types['item_sparse'] + feat_types['item_array'] + feat_types['item_continual']
            ) - set(feature.keys())
            feature = process_cold_start_feat(feature)
            for feat_id in missing_fields:
                feature[feat_id] = feat_default_value[feat_id]

            # 处理多模态embedding特征
            for feat_id in feat_types['item_emb']:
                if creative_id in mm_emb_dict[feat_id]:
                    feature[feat_id] = mm_emb_dict[feat_id][creative_id]
                else:
                    feature[feat_id] = np.zeros(EMB_SHAPE_DICT[feat_id], dtype=np.float32)

            # 添加Semantic ID特征
            for feat_id, mapping in semantic_id_mappings.items():
                semantic_feat_name = f'semantic_{feat_id}'
                if creative_id in mapping:
                    semantic_id_str = mapping[creative_id]
                    if semantic_feat_name in semantic_id_indexer and semantic_id_str in semantic_id_indexer[semantic_feat_name]:
                        feature[semantic_feat_name] = semantic_id_indexer[semantic_feat_name][semantic_id_str]
                    else:
                        feature[semantic_feat_name] = feat_default_value.get(semantic_feat_name, 0)
                else:
                    feature[semantic_feat_name] = feat_default_value.get(semantic_feat_name, 0)

            item_ids.append(item_id)
            creative_ids.append(creative_id)
            retrieval_ids.append(retrieval_id)
            features.append(feature)
            retrieve_id2creative_id[retrieval_id] = creative_id

    # 保存候选库的embedding和sid
    model.save_item_emb(item_ids, retrieval_ids, features, os.environ.get('EVAL_RESULT_PATH'))
    with open(Path(os.environ.get('EVAL_RESULT_PATH'), "retrive_id2creative_id.json"), "w") as f:
        json.dump(retrieve_id2creative_id, f)
    return retrieve_id2creative_id


def create_test_dataset_with_semantic_id(data_path, args, semantic_id_mappings):
    """
    创建支持Semantic ID的测试数据集

    Args:
        data_path: 数据路径
        args: 参数
        semantic_id_mappings: Semantic ID映射

    Returns:
        test_dataset: 测试数据集
    """
    # # 保存Semantic ID映射到临时文件
    # semantic_id_dir = Path(data_path).parent / 'semantic_ids'
    # semantic_id_dir.mkdir(exist_ok=True)
    # 
    # for feat_id, mapping in semantic_id_mappings.items():
    #     mapping_file = semantic_id_dir / f'semantic_id_mapping_feat_{feat_id}.json'
    #     with open(mapping_file, 'w') as f:
    #         json.dump(mapping, f)

    # 创建测试数据集
    test_dataset = MyTestDatasetWithSemanticID(data_path, args)
    return test_dataset


def infer():
    """主推理函数"""
    args = get_args()
    args.rqvae_model_dir = Path(os.environ.get("MODEL_OUTPUT_PATH"))
    t = args.codebook_size
    args.codebook_size = [t for i in range(args.num_codebooks)]
    args.hidden_channels[-1] = t
    data_path = os.environ.get('EVAL_DATA_PATH')

    semantic_id_dir = args.rqvae_model_dir / 'semantic_ids'
    semantic_id_dir.mkdir(parents=True, exist_ok=True)

    print("=== 开始Semantic ID推理流程 ===")

    # 1. 加载RQ-VAE模型
    print("1. 加载RQ-VAE模型...")
    rqvae_models = load_rqvae_models(args.rqvae_model_dir, args.semantic_id_features, args.device)

    if not rqvae_models:
        print("警告: 没有加载到任何RQ-VAE模型，将使用原始推理逻辑")
        # 如果没有RQ-VAE模型，回退到原始推理
        from infer_origin import infer_origin
        return infer_origin()

    # 2. 加载多模态embedding数据
    print("2. 加载多模态embedding数据...")
    from dataset import load_mm_emb
    all_mm_features = list(set(args.mm_emb_id + args.semantic_id_features))
    mm_emb_dict = load_mm_emb(Path(data_path, "creative_emb"), all_mm_features)

    # 3. 为候选集生成Semantic ID
    print("3. 为候选集生成Semantic ID...")
    candidate_path = Path(data_path, 'predict_set.jsonl')
    semantic_id_mappings = generate_semantic_ids_for_candidates(
        candidate_path, rqvae_models, args.semantic_id_features, mm_emb_dict
    )

    # 4. 保存Semantic ID映射
    print("4. 保存Semantic ID映射...")
    # semantic_id_dir = Path(data_path) / 'semantic_ids'
    # semantic_id_dir.mkdir(parents=True, exist_ok=True)
    save_semantic_id_mappings(semantic_id_mappings, semantic_id_dir)

    # 5. 创建支持Semantic ID的测试数据集
    print("5. 创建测试数据集...")
    test_dataset = create_test_dataset_with_semantic_id(data_path, args, semantic_id_mappings)
    test_loader = DataLoader(
        test_dataset, batch_size=args.batch_size, shuffle=False,
        num_workers=0, collate_fn=test_dataset.collate_fn
    )

    # 6. 加载训练好的主模型
    print("6. 加载主模型...")
    usernum, itemnum = test_dataset.usernum, test_dataset.itemnum
    feat_statistics, feat_types = test_dataset.feat_statistics, test_dataset.feature_types
    model = BaselineModel(usernum, itemnum, feat_statistics, feat_types, args).to(args.device)
    model.eval()

    # ckpt_path = get_ckpt_path()
    ckpt_path = Path(os.environ.get("MODEL_OUTPUT_PATH")) / "main_model.pt"
    model.load_state_dict(torch.load(ckpt_path, map_location=torch.device(args.device)))

    # 7. 进行用户序列预测
    print("7. 进行用户序列预测...")
    all_embs = []
    user_list = []
    for step, batch in tqdm(enumerate(test_loader), total=len(test_loader)):
        seq, token_type, seq_feat, user_id = batch
        seq = seq.to(args.device)
        logits = model.predict(seq, seq_feat, token_type)
        for i in range(logits.shape[0]):
            emb = logits[i].unsqueeze(0).detach().cpu().numpy().astype(np.float32)
            all_embs.append(emb)
        user_list += user_id

    # 8. 生成候选库的embedding
    print("8. 生成候选库embedding...")
    retrieve_id2creative_id = get_candidate_emb_with_semantic_id(
        test_dataset.indexer['i'],
        test_dataset.feature_types,
        test_dataset.feature_default_value,
        test_dataset.mm_emb_dict,
        model,
        semantic_id_mappings,
        test_dataset.semantic_id_indexer
    )

    all_embs = np.concatenate(all_embs, axis=0)

    # 9. 保存query文件并进行ANN检索
    print("9. 进行ANN检索...")
    save_emb(all_embs, Path(os.environ.get('EVAL_RESULT_PATH'), 'query.fbin'))

    # ANN 检索
    ann_cmd = (
        str(Path("/workspace", "faiss-based-ann", "faiss_demo"))
        + " --dataset_vector_file_path="
        + str(Path(os.environ.get("EVAL_RESULT_PATH"), "embedding.fbin"))
        + " --dataset_id_file_path="
        + str(Path(os.environ.get("EVAL_RESULT_PATH"), "id.u64bin"))
        + " --query_vector_file_path="
        + str(Path(os.environ.get("EVAL_RESULT_PATH"), "query.fbin"))
        + " --result_id_file_path="
        + str(Path(os.environ.get("EVAL_RESULT_PATH"), "id100.u64bin"))
        + " --query_ann_top_k=10 --faiss_M=64 --faiss_ef_construction=1280 --query_ef_search=640 --faiss_metric_type=0"
    )
    os.system(ann_cmd)

    # 10. 处理检索结果
    print("10. 处理检索结果...")
    top10s_retrieved = read_result_ids(Path(os.environ.get("EVAL_RESULT_PATH"), "id100.u64bin"))
    top10s_untrimmed = []
    for top10 in tqdm(top10s_retrieved):
        for item in top10:
            top10s_untrimmed.append(retrieve_id2creative_id.get(int(item), 0))

    top10s = [top10s_untrimmed[i : i + 10] for i in range(0, len(top10s_untrimmed), 10)]

    print("=== Semantic ID推理完成 ===")
    return top10s, user_list


if __name__ == "__main__":
    top10s, user_list = infer()
    print(f"推理完成，处理了 {len(user_list)} 个用户，每个用户返回 {len(top10s[0]) if top10s else 0} 个推荐结果")
