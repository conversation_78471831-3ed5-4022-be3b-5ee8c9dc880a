#!/usr/bin/env python3
"""
修复PyTorch 2.6 torch.load weights_only问题的工具脚本

该脚本会自动查找并修复项目中所有的torch.load调用，
添加weights_only=False参数以解决PyTorch 2.6的兼容性问题。
"""

import os
import re
from pathlib import Path


def fix_torch_load_in_file(file_path):
    """修复单个文件中的torch.load调用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 匹配torch.load调用的正则表达式
        patterns = [
            # 匹配 torch.load(path, map_location=device) 格式
            (r'torch\.load\(([^,]+),\s*map_location=([^,)]+)\)', 
             r'torch.load(\1, map_location=\2, weights_only=False)'),
            
            # 匹配 torch.load(path, map_location=device, ...) 格式（已有其他参数）
            (r'torch\.load\(([^,]+),\s*map_location=([^,)]+),\s*(?!weights_only)', 
             r'torch.load(\1, map_location=\2, weights_only=False, '),
            
            # 匹配简单的 torch.load(path) 格式
            (r'torch\.load\(([^,)]+)\)(?!\s*,)', 
             r'torch.load(\1, weights_only=False)'),
        ]
        
        modified = False
        for pattern, replacement in patterns:
            new_content = re.sub(pattern, replacement, content)
            if new_content != content:
                content = new_content
                modified = True
        
        # 如果内容有修改，写回文件
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复文件: {file_path}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ 处理文件 {file_path} 时出错: {e}")
        return False


def find_and_fix_torch_load():
    """查找并修复项目中所有的torch.load调用"""
    
    # 要搜索的文件扩展名
    extensions = ['.py']
    
    # 要排除的目录
    exclude_dirs = {
        '__pycache__', '.git', '.vscode', 'node_modules', 
        'venv', 'env', '.env', 'build', 'dist'
    }
    
    # 要排除的文件
    exclude_files = {
        'fix_torch_load.py'  # 排除自己
    }
    
    current_dir = Path('.')
    fixed_files = []
    total_files = 0
    
    print("🔍 搜索包含torch.load的Python文件...")
    
    for file_path in current_dir.rglob('*'):
        # 跳过目录
        if file_path.is_dir():
            continue
            
        # 检查文件扩展名
        if file_path.suffix not in extensions:
            continue
            
        # 检查是否在排除目录中
        if any(exclude_dir in file_path.parts for exclude_dir in exclude_dirs):
            continue
            
        # 检查是否是排除文件
        if file_path.name in exclude_files:
            continue
        
        # 检查文件是否包含torch.load
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'torch.load' in content:
                    total_files += 1
                    print(f"📁 检查文件: {file_path}")
                    
                    if fix_torch_load_in_file(file_path):
                        fixed_files.append(str(file_path))
                        
        except Exception as e:
            print(f"⚠️  无法读取文件 {file_path}: {e}")
    
    print(f"\n📊 修复完成!")
    print(f"   检查文件数: {total_files}")
    print(f"   修复文件数: {len(fixed_files)}")
    
    if fixed_files:
        print(f"\n✅ 已修复的文件:")
        for file_path in fixed_files:
            print(f"   - {file_path}")
    else:
        print(f"\n✅ 没有需要修复的文件")


def create_safe_load_function():
    """创建一个安全的torch.load包装函数"""
    
    safe_load_code = '''"""
安全的torch.load包装函数

解决PyTorch 2.6 weights_only默认值变更问题
"""

import torch
import warnings


def safe_torch_load(file_path, map_location=None, **kwargs):
    """
    安全的torch.load包装函数
    
    Args:
        file_path: 模型文件路径
        map_location: 设备映射
        **kwargs: 其他参数
    
    Returns:
        加载的模型数据
    """
    try:
        # 首先尝试使用weights_only=False
        return torch.load(file_path, map_location=map_location, weights_only=False, **kwargs)
    except Exception as e:
        # 如果失败，尝试其他方法
        warnings.warn(f"使用weights_only=False加载失败: {e}")
        
        try:
            # 尝试不指定weights_only参数
            return torch.load(file_path, map_location=map_location, **kwargs)
        except Exception as e2:
            # 如果还是失败，抛出原始错误
            raise e


def load_rqvae_checkpoint(model_path, device):
    """
    专门用于加载RQ-VAE模型的函数
    
    Args:
        model_path: 模型文件路径
        device: 目标设备
    
    Returns:
        checkpoint: 加载的检查点数据
    """
    checkpoint = safe_torch_load(model_path, map_location=device)
    
    # 处理不同的保存格式
    if isinstance(checkpoint, dict):
        if 'model_state_dict' in checkpoint:
            return checkpoint
        elif 'model' in checkpoint:
            return checkpoint
        else:
            # 假设整个字典就是state_dict
            return {'model_state_dict': checkpoint}
    else:
        # 假设直接是state_dict
        return {'model_state_dict': checkpoint}


def load_model_state_dict(model, checkpoint):
    """
    安全地加载模型状态字典
    
    Args:
        model: PyTorch模型
        checkpoint: 检查点数据
    """
    if isinstance(checkpoint, dict):
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        elif 'model' in checkpoint:
            model.load_state_dict(checkpoint['model'])
        else:
            model.load_state_dict(checkpoint)
    else:
        model.load_state_dict(checkpoint)
'''
    
    # 保存到utils文件
    utils_file = Path('torch_load_utils.py')
    with open(utils_file, 'w', encoding='utf-8') as f:
        f.write(safe_load_code)
    
    print(f"✅ 创建安全加载工具: {utils_file}")


if __name__ == '__main__':
    print("🚀 PyTorch 2.6 torch.load 兼容性修复工具")
    print("=" * 50)
    
    # 修复现有文件
    find_and_fix_torch_load()
    
    # 创建安全加载工具
    create_safe_load_function()
    
    print("\n🎉 修复完成!")
    print("\n💡 使用建议:")
    print("   1. 运行修复后，重新测试你的代码")
    print("   2. 如果还有问题，可以使用 torch_load_utils.py 中的安全函数")
    print("   3. 在新代码中，建议直接使用 weights_only=False 参数")
