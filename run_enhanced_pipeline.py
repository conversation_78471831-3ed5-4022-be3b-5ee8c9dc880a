#!/usr/bin/env python3
"""
增强版生成式推荐完整流程

整合所有优化方案：
1. 分层Transformer + 多尺度时序建模
2. 语义感知RQ-VAE + 渐进式量化
3. 意图感知序列建模 + 动态负采样
4. 多任务对比损失 + 自适应权重
5. 多阶段生成式检索 + 多样性保证
6. 任务自适应训练策略
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path
import json
import torch
import numpy as np
from torch.utils.data import DataLoader
from tqdm import tqdm

# 导入增强版组件
from model_enhanced import EnhancedGenerativeModel
from model_rqvae_enhanced import TaskAdaptiveRQVAE
from dataset_enhanced import IntentAwareDataset
from loss_enhanced import EnhancedLossFunction
from inference_enhanced import MultiStageGenerativeRetrieval


def get_args():
    parser = argparse.ArgumentParser(description='增强版生成式推荐完整流程')
    
    # 基础参数
    parser.add_argument('--features', nargs='+', default=['83', '84'], 
                       help='要处理的高维特征ID')
    parser.add_argument('--device', default='cuda', type=str)
    parser.add_argument('--data_dir', default=None, type=str)
    
    # 模型架构参数
    parser.add_argument('--hidden_units', default=64, type=int, help='隐藏层维度（增加）')
    parser.add_argument('--num_blocks', default=3, type=int, help='Transformer层数（增加）')
    parser.add_argument('--num_heads', default=4, type=int, help='注意力头数（增加）')
    parser.add_argument('--dropout_rate', default=0.1, type=float)
    parser.add_argument('--norm_first', action='store_true', help='Pre-LN结构')
    
    # 序列建模参数
    parser.add_argument('--maxlen', default=101, type=int)
    parser.add_argument('--intent_window', default=10, type=int, help='意图窗口大小')
    
    # RQ-VAE参数
    parser.add_argument('--rqvae_epochs', default=50, type=int)
    parser.add_argument('--rqvae_batch_size', default=256, type=int)
    parser.add_argument('--latent_dim', default=128, type=int, help='潜在空间维度（增加）')
    parser.add_argument('--num_codebooks', default=3, type=int, help='码本数量（增加）')
    parser.add_argument('--codebook_size', default=512, type=int, help='码本大小（增加）')
    
    # 训练参数
    parser.add_argument('--main_epochs', default=10, type=int, help='主模型训练轮数（增加）')
    parser.add_argument('--batch_size', default=64, type=int, help='批次大小（减少以适应更大模型）')
    parser.add_argument('--lr', default=5e-4, type=float, help='学习率（调整）')
    parser.add_argument('--weight_decay', default=1e-4, type=float, help='权重衰减')
    
    # 损失函数参数
    parser.add_argument('--temperature', default=0.07, type=float, help='对比学习温度')
    parser.add_argument('--margin', default=0.5, type=float, help='排序损失边界')
    parser.add_argument('--temporal_alpha', default=0.3, type=float, help='时序损失权重')
    
    # 推理参数
    parser.add_argument('--num_candidates', default=1000, type=int, help='候选集大小')
    parser.add_argument('--num_final', default=10, type=int, help='最终推荐数量')
    
    # 实验控制
    parser.add_argument('--experiment_name', default='enhanced_generative', type=str)
    parser.add_argument('--skip_rqvae', action='store_true')
    parser.add_argument('--only_rqvae', action='store_true')
    parser.add_argument('--enable_wandb', action='store_true', help='启用wandb日志')
    
    return parser.parse_args()


def setup_experiment(args):
    """设置实验环境"""
    # 创建实验目录
    exp_dir = Path(f'experiments/{args.experiment_name}')
    exp_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置环境变量
    os.environ['TRAIN_CKPT_PATH'] = str(exp_dir / 'checkpoints')
    os.environ['TRAIN_LOG_PATH'] = str(exp_dir / 'logs')
    os.environ['TRAIN_TF_EVENTS_PATH'] = str(exp_dir / 'tf_events')
    
    if args.data_dir is None:
        args.data_dir = os.environ.get('TRAIN_DATA_PATH', './data/TencentGR_1k')
    
    # 创建必要目录
    for path in [os.environ['TRAIN_CKPT_PATH'], os.environ['TRAIN_LOG_PATH'], 
                 os.environ['TRAIN_TF_EVENTS_PATH']]:
        Path(path).mkdir(parents=True, exist_ok=True)
    
    # 保存实验配置
    config_path = exp_dir / 'config.json'
    with open(config_path, 'w') as f:
        json.dump(vars(args), f, indent=2)
    
    print(f"实验目录: {exp_dir}")
    return exp_dir


def train_enhanced_rqvae(feature_id, args, exp_dir):
    """训练增强版RQ-VAE"""
    print(f"\n{'='*60}")
    print(f"训练增强版RQ-VAE - 特征ID: {feature_id}")
    print(f"{'='*60}")
    
    from model_rqvae_enhanced import TaskAdaptiveRQVAE
    from dataset import load_mm_emb
    
    # 加载数据
    mm_emb_dict = load_mm_emb(Path(args.data_dir, "creative_emb"), [feature_id])
    embeddings = list(mm_emb_dict[feature_id].values())
    
    # 转换为tensor
    embeddings_tensor = torch.stack([
        torch.tensor(emb, dtype=torch.float32) for emb in embeddings
    ])
    
    # 创建增强版RQ-VAE
    SHAPE_DICT = {"81": 32, "82": 1024, "83": 3584, "84": 4096, "85": 3584, "86": 3584}
    input_dim = SHAPE_DICT[feature_id]
    
    model = TaskAdaptiveRQVAE(
        input_dims=[input_dim],
        hidden_channels=[512, 256],
        latent_dim=args.latent_dim,
        num_codebooks=args.num_codebooks,
        codebook_size=[args.codebook_size] * args.num_codebooks,
        shared_codebook=False,
        kmeans_method='kmeans',
        kmeans_iters=50,
        distances_method='l2',
        loss_beta=0.25,
        device=args.device
    ).to(args.device)
    
    # 训练
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-4)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.rqvae_epochs)
    
    model.train()
    best_loss = float('inf')
    
    for epoch in range(args.rqvae_epochs):
        total_loss = 0
        num_batches = 0
        
        # 创建数据加载器
        dataset = torch.utils.data.TensorDataset(embeddings_tensor)
        dataloader = torch.utils.data.DataLoader(
            dataset, batch_size=args.rqvae_batch_size, shuffle=True
        )
        
        for batch_idx, (batch_embs,) in enumerate(dataloader):
            batch_embs = batch_embs.to(args.device)
            
            optimizer.zero_grad()
            
            # 前向传播
            x_hat, semantic_ids, recon_loss, rqvae_loss, total_batch_loss = model([batch_embs])
            
            # 反向传播
            total_batch_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_loss += total_batch_loss.item()
            num_batches += 1
        
        scheduler.step()
        avg_loss = total_loss / num_batches
        
        print(f"Epoch {epoch+1}/{args.rqvae_epochs}, Loss: {avg_loss:.4f}")
        
        # 保存最佳模型
        if avg_loss < best_loss:
            best_loss = avg_loss
            model_path = exp_dir / 'rqvae_models' / f'rqvae_feat_{feature_id}_best.pt'
            model_path.parent.mkdir(exist_ok=True)
            torch.save(model.state_dict(), model_path)
    
    print(f"RQ-VAE训练完成，最佳损失: {best_loss:.4f}")
    return True


def train_enhanced_main_model(args, exp_dir):
    """训练增强版主模型"""
    print(f"\n{'='*60}")
    print("训练增强版主模型")
    print(f"{'='*60}")
    
    # 创建增强版数据集
    dataset = IntentAwareDataset(args.data_dir, args)
    
    # 划分训练集和验证集
    train_size = int(0.9 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    train_loader = DataLoader(
        train_dataset, batch_size=args.batch_size, shuffle=True, 
        num_workers=0, collate_fn=dataset.collate_fn
    )
    val_loader = DataLoader(
        val_dataset, batch_size=args.batch_size, shuffle=False,
        num_workers=0, collate_fn=dataset.collate_fn
    )
    
    # 创建增强版模型
    usernum, itemnum = dataset.usernum, dataset.itemnum
    feat_statistics, feat_types = dataset.feat_statistics, dataset.feature_types
    
    model = EnhancedGenerativeModel(usernum, itemnum, feat_statistics, feat_types, args).to(args.device)
    
    # 创建增强版损失函数
    criterion = EnhancedLossFunction(args)
    
    # 优化器
    optimizer = torch.optim.AdamW(
        model.parameters(), lr=args.lr, weight_decay=args.weight_decay
    )
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.main_epochs)
    
    # 训练循环
    best_val_loss = float('inf')
    
    for epoch in range(args.main_epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        train_batches = 0
        
        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1} Training"):
            # 解包增强版数据
            (seq, pos, neg, token_type, next_token_type, next_action_type,
             intent_type, session_position, timestamps, seq_feat, pos_feat, neg_feat) = batch
            
            seq = seq.to(args.device)
            pos = pos.to(args.device)
            neg = neg.to(args.device)
            token_type = token_type.to(args.device)
            next_token_type = next_token_type.to(args.device)
            # 转移到设备
            timestamps = timestamps.to(args.device)
            session_position = session_position.to(args.device)
            
            optimizer.zero_grad()
            
            # 前向传播
            pos_logits, neg_logits, contrastive_pos, contrastive_neg, user_repr, pos_repr, neg_repr = model(
                seq, pos, neg, token_type, next_token_type, next_action_type,
                seq_feat, pos_feat, neg_feat
            )
            
            # 计算损失-origin
            loss, loss_info = criterion(
                pos_logits, neg_logits, user_repr, pos_repr, neg_repr, None,
                next_token_type, timestamps=timestamps, session_positions=session_position
            )
            
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            train_batches += 1
        
        scheduler.step()
        avg_train_loss = train_loss / train_batches
        
        # 验证阶段
        model.eval()
        val_loss = 0
        val_batches = 0
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f"Epoch {epoch+1} Validation"):
                (seq, pos, neg, token_type, next_token_type, next_action_type,
                 intent_type, session_position, timestamps, seq_feat, pos_feat, neg_feat) = batch
                
                seq = seq.to(args.device)
                pos = pos.to(args.device)
                neg = neg.to(args.device)
                token_type = token_type.to(args.device)
                next_token_type = next_token_type.to(args.device)
                
                pos_logits, neg_logits, contrastive_pos, contrastive_neg, user_repr, pos_repr, neg_repr = model(
                    seq, pos, neg, token_type, next_token_type, next_action_type,
                    seq_feat, pos_feat, neg_feat
                )
                
                loss, loss_info = criterion(
                    pos_logits, neg_logits, None, None, None, None,
                    next_token_type, timestamps=None, session_positions=session_position
                )
                
                val_loss += loss.item()
                val_batches += 1
        
        avg_val_loss = val_loss / val_batches
        
        print(f"Epoch {epoch+1}/{args.main_epochs}")
        print(f"  Train Loss: {avg_train_loss:.4f}")
        print(f"  Val Loss: {avg_val_loss:.4f}")
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            model_path = exp_dir / 'checkpoints' / 'best_model.pt'
            model_path.parent.mkdir(exist_ok=True)
            torch.save(model.state_dict(), model_path)
    
    print(f"主模型训练完成，最佳验证损失: {best_val_loss:.4f}")
    return True


def main():
    args = get_args()
    
    # 设置实验环境
    exp_dir = setup_experiment(args)
    
    print("增强版生成式推荐完整流程")
    print(f"实验名称: {args.experiment_name}")
    print(f"处理特征: {args.features}")
    print(f"设备: {args.device}")
    
    success_features = []
    failed_features = []
    
    # 第一步：训练增强版RQ-VAE
    if not args.skip_rqvae:
        for feature_id in args.features:
            try:
                if train_enhanced_rqvae(feature_id, args, exp_dir):
                    success_features.append(feature_id)
                else:
                    failed_features.append(feature_id)
            except Exception as e:
                print(f"特征 {feature_id} 的RQ-VAE训练失败: {e}")
                failed_features.append(feature_id)
    else:
        success_features = args.features
    
    # 第二步：训练增强版主模型
    if not args.only_rqvae and success_features:
        try:
            args.semantic_id_features = success_features
            train_enhanced_main_model(args, exp_dir)
            print("增强版主模型训练成功")
        except Exception as e:
            print(f"主模型训练失败: {e}")
    
    # 打印总结
    print(f"\n{'='*60}")
    print("训练总结")
    print(f"{'='*60}")
    print(f"成功处理的特征: {success_features}")
    if failed_features:
        print(f"失败的特征: {failed_features}")
    print(f"实验结果保存在: {exp_dir}")


if __name__ == '__main__':
    main()
