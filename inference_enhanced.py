"""
增强版推理框架

主要改进：
1. 多阶段生成式检索
2. 动态候选集生成
3. 交互式重排序
4. 多样性保证机制
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Tuple
import faiss


class MultiStageGenerativeRetrieval(nn.Module):
    """多阶段生成式检索"""
    
    def __init__(self, model, item_embeddings, args):
        super().__init__()
        self.model = model
        self.item_embeddings = item_embeddings  # [num_items, hidden_dim]
        self.hidden_dim = args.hidden_units
        self.device = args.device
        
        # 构建多层次索引
        self._build_hierarchical_index()
        
        # 交互式重排序网络
        self.reranker = InteractiveReranker(args.hidden_units)
        
        # 多样性控制器
        self.diversity_controller = DiversityController(args.hidden_units)
        
    def _build_hierarchical_index(self):
        """构建层次化索引"""
        # 1. 粗粒度索引（聚类中心）
        self.coarse_index = self._build_coarse_index()
        
        # 2. 细粒度索引（精确检索）
        self.fine_index = self._build_fine_index()
        
    def _build_coarse_index(self, num_clusters=1000):
        """构建粗粒度索引"""
        embeddings = self.item_embeddings.cpu().numpy()
        
        # 使用K-means聚类
        kmeans = faiss.Kmeans(self.hidden_dim, num_clusters, niter=20, verbose=False)
        kmeans.train(embeddings)
        
        # 构建索引
        index = faiss.IndexFlatIP(self.hidden_dim)
        index.add(kmeans.centroids)
        
        # 保存聚类分配
        _, cluster_assignments = kmeans.index.search(embeddings, 1)
        self.cluster_assignments = cluster_assignments.flatten()
        self.cluster_centroids = torch.tensor(kmeans.centroids, device=self.device)
        
        return index
    
    def _build_fine_index(self):
        """构建细粒度索引"""
        embeddings = self.item_embeddings.cpu().numpy()
        
        # 使用HNSW索引
        index = faiss.IndexHNSWFlat(self.hidden_dim)
        index.hnsw.efConstruction = 200
        index.add(embeddings)
        
        return index
    
    def retrieve(self, user_emb, num_candidates=100, num_final=10):
        """多阶段检索"""
        batch_size = user_emb.size(0)
        
        # 阶段1：粗粒度检索
        coarse_candidates = self._coarse_retrieval(user_emb, num_candidates * 10)
        
        # 阶段2：细粒度检索
        fine_candidates = self._fine_retrieval(user_emb, coarse_candidates, num_candidates)
        
        # 阶段3：交互式重排序
        reranked_candidates = self._interactive_reranking(user_emb, fine_candidates)
        
        # 阶段4：多样性保证
        final_results = self._ensure_diversity(user_emb, reranked_candidates, num_final)
        
        return final_results
    
    def _coarse_retrieval(self, user_emb, num_candidates):
        """粗粒度检索"""
        batch_size = user_emb.size(0)
        user_emb_np = user_emb.cpu().numpy()
        
        # 检索最相关的聚类
        num_clusters = min(num_candidates // 10, 100)
        _, cluster_ids = self.coarse_index.search(user_emb_np, num_clusters)
        
        # 收集候选items
        candidates = []
        for b in range(batch_size):
            batch_candidates = []
            for cluster_id in cluster_ids[b]:
                # 获取该聚类中的所有items
                cluster_items = np.where(self.cluster_assignments == cluster_id)[0]
                batch_candidates.extend(cluster_items.tolist())
            
            # 限制候选数量
            if len(batch_candidates) > num_candidates:
                batch_candidates = batch_candidates[:num_candidates]
            
            candidates.append(batch_candidates)
        
        return candidates
    
    def _fine_retrieval(self, user_emb, coarse_candidates, num_candidates):
        """细粒度检索"""
        batch_size = user_emb.size(0)
        fine_candidates = []
        
        for b in range(batch_size):
            if not coarse_candidates[b]:
                fine_candidates.append([])
                continue
            
            # 获取候选item的embeddings
            candidate_ids = torch.tensor(coarse_candidates[b], device=self.device)
            candidate_embs = self.item_embeddings[candidate_ids]
            
            # 计算相似度
            user_emb_single = user_emb[b].unsqueeze(0)  # [1, hidden_dim]
            similarities = torch.mm(user_emb_single, candidate_embs.t()).squeeze(0)
            
            # 选择top-k
            _, top_indices = torch.topk(similarities, min(num_candidates, len(candidate_ids)))
            selected_candidates = candidate_ids[top_indices].cpu().numpy().tolist()
            
            fine_candidates.append(selected_candidates)
        
        return fine_candidates
    
    def _interactive_reranking(self, user_emb, candidates):
        """交互式重排序"""
        batch_size = user_emb.size(0)
        reranked_candidates = []
        
        for b in range(batch_size):
            if not candidates[b]:
                reranked_candidates.append([])
                continue
            
            candidate_ids = torch.tensor(candidates[b], device=self.device)
            candidate_embs = self.item_embeddings[candidate_ids]
            
            # 交互式评分
            interaction_scores = self.reranker(
                user_emb[b].unsqueeze(0).expand(len(candidate_ids), -1),
                candidate_embs
            )
            
            # 重新排序
            _, rerank_indices = torch.sort(interaction_scores, descending=True)
            reranked_ids = candidate_ids[rerank_indices].cpu().numpy().tolist()
            
            reranked_candidates.append(reranked_ids)
        
        return reranked_candidates
    
    def _ensure_diversity(self, user_emb, candidates, num_final):
        """确保多样性"""
        batch_size = user_emb.size(0)
        diverse_results = []
        
        for b in range(batch_size):
            if not candidates[b]:
                diverse_results.append([])
                continue
            
            candidate_ids = torch.tensor(candidates[b], device=self.device)
            candidate_embs = self.item_embeddings[candidate_ids]
            
            # 多样性选择
            selected_indices = self.diversity_controller.select_diverse_items(
                user_emb[b], candidate_embs, num_final
            )
            
            diverse_ids = candidate_ids[selected_indices].cpu().numpy().tolist()
            diverse_results.append(diverse_ids)
        
        return diverse_results


class InteractiveReranker(nn.Module):
    """交互式重排序网络"""
    
    def __init__(self, hidden_dim):
        super().__init__()
        
        # 用户-物品交互网络
        self.interaction_net = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1)
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=4, batch_first=True)
        
    def forward(self, user_emb, item_embs):
        """
        Args:
            user_emb: [num_items, hidden_dim]
            item_embs: [num_items, hidden_dim]
        """
        # 1. 基础交互特征
        interaction_features = torch.cat([user_emb, item_embs], dim=-1)
        base_scores = self.interaction_net(interaction_features).squeeze(-1)
        
        # 2. 注意力增强
        # 将item作为query，user作为key和value
        attn_output, _ = self.attention(
            item_embs.unsqueeze(0),  # [1, num_items, hidden_dim]
            user_emb.unsqueeze(0),   # [1, num_items, hidden_dim]
            user_emb.unsqueeze(0)    # [1, num_items, hidden_dim]
        )
        
        # 3. 组合评分
        attn_scores = torch.sum(attn_output.squeeze(0) * item_embs, dim=-1)
        final_scores = base_scores + 0.1 * attn_scores
        
        return final_scores


class DiversityController(nn.Module):
    """多样性控制器"""
    
    def __init__(self, hidden_dim, diversity_weight=0.3):
        super().__init__()
        self.diversity_weight = diversity_weight
        self.hidden_dim = hidden_dim
        
    def select_diverse_items(self, user_emb, candidate_embs, num_select):
        """选择多样化的items"""
        num_candidates = candidate_embs.size(0)
        if num_candidates <= num_select:
            return torch.arange(num_candidates)
        
        # 计算相关性分数
        relevance_scores = torch.sum(user_emb.unsqueeze(0) * candidate_embs, dim=-1)
        
        # 贪心多样性选择
        selected_indices = []
        remaining_indices = list(range(num_candidates))
        
        # 选择最相关的item作为第一个
        first_idx = torch.argmax(relevance_scores).item()
        selected_indices.append(first_idx)
        remaining_indices.remove(first_idx)
        
        # 迭代选择剩余items
        for _ in range(num_select - 1):
            if not remaining_indices:
                break
            
            best_score = -float('inf')
            best_idx = None
            
            for idx in remaining_indices:
                # 相关性分数
                relevance = relevance_scores[idx].item()
                
                # 多样性分数（与已选择items的最小距离）
                if selected_indices:
                    selected_embs = candidate_embs[selected_indices]
                    current_emb = candidate_embs[idx].unsqueeze(0)
                    
                    # 计算与已选择items的相似度
                    similarities = torch.sum(current_emb * selected_embs, dim=-1)
                    diversity = -torch.max(similarities).item()  # 负相似度作为多样性
                else:
                    diversity = 0
                
                # 组合分数
                combined_score = (1 - self.diversity_weight) * relevance + self.diversity_weight * diversity
                
                if combined_score > best_score:
                    best_score = combined_score
                    best_idx = idx
            
            if best_idx is not None:
                selected_indices.append(best_idx)
                remaining_indices.remove(best_idx)
        
        return torch.tensor(selected_indices, device=candidate_embs.device)


class AdaptiveRetrievalStrategy:
    """自适应检索策略"""
    
    def __init__(self, strategies=['semantic', 'collaborative', 'content']):
        self.strategies = strategies
        self.strategy_weights = {s: 1.0 / len(strategies) for s in strategies}
        self.performance_history = {s: [] for s in strategies}
        
    def select_strategy(self, user_profile, context):
        """根据用户画像和上下文选择检索策略"""
        # 基于用户活跃度选择策略
        if user_profile.get('activity_level', 'medium') == 'high':
            # 活跃用户：更多协同过滤
            return 'collaborative'
        elif user_profile.get('activity_level', 'medium') == 'low':
            # 新用户：更多内容推荐
            return 'content'
        else:
            # 中等活跃：语义推荐
            return 'semantic'
    
    def update_performance(self, strategy, performance_score):
        """更新策略性能"""
        self.performance_history[strategy].append(performance_score)
        
        # 保持最近100次记录
        if len(self.performance_history[strategy]) > 100:
            self.performance_history[strategy] = self.performance_history[strategy][-100:]
        
        # 更新权重
        self._update_weights()
    
    def _update_weights(self):
        """基于历史性能更新策略权重"""
        total_performance = 0
        strategy_performances = {}
        
        for strategy in self.strategies:
            if self.performance_history[strategy]:
                avg_performance = np.mean(self.performance_history[strategy][-20:])  # 最近20次
                strategy_performances[strategy] = avg_performance
                total_performance += avg_performance
        
        # 归一化权重
        if total_performance > 0:
            for strategy in self.strategies:
                if strategy in strategy_performances:
                    self.strategy_weights[strategy] = strategy_performances[strategy] / total_performance
                else:
                    self.strategy_weights[strategy] = 1.0 / len(self.strategies)
