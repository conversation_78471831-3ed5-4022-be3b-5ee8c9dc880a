"""
支持Semantic ID的主训练脚本

使用RQ-VAE压缩后的Semantic ID特征进行训练，解决高维embedding内存问题
"""

import argparse
import json
import os
import time
from pathlib import Path

import numpy as np
import torch
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm

from dataset_with_semantic_id import OptimizedMyDatasetWithSemanticID, prepare_semantic_id_features
from model import BaselineModel


def get_args():
    parser = argparse.ArgumentParser()

    # Train params
    parser.add_argument('--batch_size', default=128, type=int)
    parser.add_argument('--lr', default=0.001, type=float)
    parser.add_argument('--maxlen', default=101, type=int)

    # Baseline Model construction
    parser.add_argument('--hidden_units', default=32, type=int)
    parser.add_argument('--num_blocks', default=1, type=int)
    parser.add_argument('--num_epochs', default=3, type=int)
    parser.add_argument('--num_heads', default=1, type=int)
    parser.add_argument('--dropout_rate', default=0.2, type=float)
    parser.add_argument('--l2_emb', default=0.0, type=float)
    parser.add_argument('--device', default='cuda', type=str)
    parser.add_argument('--inference_only', action='store_true')
    parser.add_argument('--state_dict_path', default=None, type=str)
    parser.add_argument('--norm_first', action='store_true')

    # 原始多模态特征ID（只使用低维的）
    parser.add_argument('--mm_emb_id', nargs='+', default=['81'], type=str, 
                       choices=[str(s) for s in range(81, 87)])
    
    # 新增：Semantic ID特征（高维embedding压缩后的特征）
    parser.add_argument('--semantic_id_features', nargs='+', default=['83', '84'], type=str,
                       help='使用RQ-VAE压缩的高维embedding特征ID')
    
    # 实验标识
    parser.add_argument('--experiment_name', default='baseline_with_semantic_id', type=str,
                       help='实验名称，用于区分不同的实验')
    parser.add_argument('--semantic_id_dir', default='semantic_ids', type=str, help='Semantic ID保存目录')
    parser.add_argument('--data_dir', default=None, type=str, help='数据目录')
    parser.add_argument('--output_dir', default=None, type=str, help='模型目录')

    args = parser.parse_args()
    return args


def main():
    # 设置环境变量（如果没有设置的话）
    if 'TRAIN_LOG_PATH' not in os.environ:
        os.environ['TRAIN_LOG_PATH'] = './logs'
    if 'TRAIN_TF_EVENTS_PATH' not in os.environ:
        os.environ['TRAIN_TF_EVENTS_PATH'] = './tf_events'
    if 'TRAIN_DATA_PATH' not in os.environ:
        os.environ['TRAIN_DATA_PATH'] = './data/TencentGR_1k'
    if 'TRAIN_CKPT_PATH' not in os.environ:
        os.environ['TRAIN_CKPT_PATH'] = './checkpoints'

    args = get_args()
    if(args.data_dir is None):
        args.data_dir = os.environ.get('TRAIN_DATA_PATH')
    
    # 创建实验特定的目录
    exp_log_path = Path(os.environ.get('TRAIN_LOG_PATH')) / args.experiment_name
    exp_tf_events_path = Path(os.environ.get('TRAIN_TF_EVENTS_PATH')) / args.experiment_name
    # exp_ckpt_path = Path(os.environ.get('TRAIN_CKPT_PATH')) / args.experiment_name
    exp_ckpt_path = Path(args.output_dir)

    exp_log_path.mkdir(parents=True, exist_ok=True)
    exp_tf_events_path.mkdir(parents=True, exist_ok=True)
    exp_ckpt_path.mkdir(parents=True, exist_ok=True)
    
    log_file = open(exp_log_path / 'train.log', 'w')
    writer = SummaryWriter(str(exp_tf_events_path))
    
    data_path = args.data_dir
    
    print("=" * 60)
    print(f"实验名称: {args.experiment_name}")
    print(f"使用的多模态特征: {args.mm_emb_id}")
    print(f"使用的Semantic ID特征: {args.semantic_id_features}")
    print("=" * 60)
    
    # 准备Semantic ID特征
    if args.semantic_id_features:
        print("准备Semantic ID特征...")
        prepare_semantic_id_features(data_path, args.semantic_id_features, args)
    
    # 创建数据集
    print("加载数据集...")
    dataset = OptimizedMyDatasetWithSemanticID(data_path, args)
    
    train_dataset, valid_dataset = torch.utils.data.random_split(dataset, [0.9, 0.1])
    train_loader = DataLoader(
        train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=0, collate_fn=dataset.collate_fn
    )
    valid_loader = DataLoader(
        valid_dataset, batch_size=args.batch_size, shuffle=False, num_workers=0, collate_fn=dataset.collate_fn
    )
    
    usernum, itemnum = dataset.usernum, dataset.itemnum
    feat_statistics, feat_types = dataset.feat_statistics, dataset.feature_types
    
    print(f"用户数量: {usernum}, 物品数量: {itemnum}")
    print(f"特征统计: {len(feat_statistics)} 个特征")
    
    # 打印特征类型信息
    for feat_type, feat_list in feat_types.items():
        if feat_list:
            print(f"{feat_type}: {len(feat_list)} 个特征")
    
    # 创建模型
    print("创建模型...")
    model = BaselineModel(usernum, itemnum, feat_statistics, feat_types, args).to(args.device)
    
    # 初始化模型参数
    for name, param in model.named_parameters():
        try:
            torch.nn.init.xavier_normal_(param.data)
        except Exception:
            pass

    model.pos_emb.weight.data[0, :] = 0
    model.item_emb.weight.data[0, :] = 0
    model.user_emb.weight.data[0, :] = 0

    for k in model.sparse_emb:
        model.sparse_emb[k].weight.data[0, :] = 0

    epoch_start_idx = 1

    # 加载预训练模型（如果指定）
    if args.state_dict_path is not None:
        try:
            model.load_state_dict(torch.load(args.state_dict_path, map_location=torch.device(args.device), weights_only=False))
            tail = args.state_dict_path[args.state_dict_path.find('epoch=') + 6 :]
            epoch_start_idx = int(tail[: tail.find('.')]) + 1
            print(f"加载预训练模型: {args.state_dict_path}")
        except:
            print('failed loading state_dicts, pls check file path: ', end="")
            print(args.state_dict_path)
            raise RuntimeError('failed loading state_dicts, pls check file path!')

    # 损失函数和优化器
    bce_criterion = torch.nn.BCEWithLogitsLoss(reduction='mean')
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr, betas=(0.9, 0.98))

    best_val_loss = float('inf')
    global_step = 0
    
    print("开始训练...")
    print("=" * 60)
    
    for epoch in range(epoch_start_idx, args.num_epochs + 1):
        model.train()
        if args.inference_only:
            break
            
        epoch_train_loss = 0.0
        num_batches = 0
        
        for step, batch in tqdm(enumerate(train_loader), total=len(train_loader), 
                               desc=f'Epoch {epoch}/{args.num_epochs}'):
            seq, pos, neg, token_type, next_token_type, next_action_type, seq_feat, pos_feat, neg_feat = batch
            seq = seq.to(args.device)
            pos = pos.to(args.device)
            neg = neg.to(args.device)
            
            pos_logits, neg_logits = model(
                seq, pos, neg, token_type, next_token_type, next_action_type, seq_feat, pos_feat, neg_feat
            )
            pos_labels, neg_labels = torch.ones(pos_logits.shape, device=args.device), torch.zeros(
                neg_logits.shape, device=args.device
            )
            
            optimizer.zero_grad()
            indices = np.where(next_token_type == 1)
            loss = bce_criterion(pos_logits[indices], pos_labels[indices])
            loss += bce_criterion(neg_logits[indices], neg_labels[indices])

            # L2正则化
            for param in model.item_emb.parameters():
                loss += args.l2_emb * torch.norm(param)
            
            loss.backward()
            optimizer.step()
            
            epoch_train_loss += loss.item()
            num_batches += 1

            # 记录日志
            log_json = json.dumps({
                'global_step': global_step, 
                'loss': loss.item(), 
                'epoch': epoch, 
                'time': time.time()
            })
            log_file.write(log_json + '\n')
            log_file.flush()

            writer.add_scalar('Loss/train', loss.item(), global_step)
            global_step += 1

        # 验证
        model.eval()
        valid_loss_sum = 0
        valid_batches = 0
        
        with torch.no_grad():
            for step, batch in tqdm(enumerate(valid_loader), total=len(valid_loader), desc='Validation'):
                seq, pos, neg, token_type, next_token_type, next_action_type, seq_feat, pos_feat, neg_feat = batch
                seq = seq.to(args.device)
                pos = pos.to(args.device)
                neg = neg.to(args.device)
                
                pos_logits, neg_logits = model(
                    seq, pos, neg, token_type, next_token_type, next_action_type, seq_feat, pos_feat, neg_feat
                )
                pos_labels, neg_labels = torch.ones(pos_logits.shape, device=args.device), torch.zeros(
                    neg_logits.shape, device=args.device
                )
                
                indices = np.where(next_token_type == 1)
                loss = bce_criterion(pos_logits[indices], pos_labels[indices])
                loss += bce_criterion(neg_logits[indices], neg_labels[indices])
                
                valid_loss_sum += loss.item()
                valid_batches += 1

        avg_train_loss = epoch_train_loss / num_batches
        avg_valid_loss = valid_loss_sum / valid_batches
        
        print(f'Epoch {epoch}: Train Loss={avg_train_loss:.4f}, Valid Loss={avg_valid_loss:.4f}')
        
        writer.add_scalar('Loss/valid', avg_valid_loss, global_step)

        # 保存模型
        # save_dir = exp_ckpt_path / f"epoch_{epoch}_valid_loss_{avg_valid_loss:.4f}"
        save_dir = exp_ckpt_path / f"global_step_{global_step}_epoch_{epoch}_valid_loss_{avg_valid_loss:.4f}"
        save_dir.mkdir(parents=True, exist_ok=True)
        torch.save(model.state_dict(), save_dir / "model.pt")
        
        # 保存最佳模型
        if avg_valid_loss < best_val_loss:
            best_val_loss = avg_valid_loss
            best_model_dir = exp_ckpt_path / "global_step_best"
            best_model_dir.mkdir(parents=True, exist_ok=True)
            torch.save(model.state_dict(), best_model_dir / "main_model.pt")
            print(f"保存最佳模型，验证损失: {best_val_loss:.4f}")

    print("=" * 60)
    print("训练完成！")
    print(f"最佳验证损失: {best_val_loss:.4f}")
    print(f"模型保存在: {exp_ckpt_path}")
    print("=" * 60)
    
    writer.close()
    log_file.close()


if __name__ == '__main__':
    main()
